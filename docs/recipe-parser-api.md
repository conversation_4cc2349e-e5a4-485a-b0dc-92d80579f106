# 菜谱解析API文档

## 概述

菜谱解析功能可以从GitHub上的Markdown格式菜谱文件中提取结构化的菜谱信息，包括菜名、食材、制作步骤等，并可以保存到数据库中。

## 解析的白灼菜心菜谱信息

基于GitHub链接：https://github.com/Anduin2017/HowToCook/blob/master/dishes/vegetable_dish/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83.md

### 解析结果

**基本信息：**
- 菜名：白灼菜心
- 描述：白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。减肥或者是快速解决绿叶菜的绝佳方式。
- 难度等级：2 (★★)
- 份数：2人份

**食材列表：**
- 新鲜菜心 250g (主料)
- 生抽 5g (调料)
- 蚝油 5g (调料)
- 盐 5g (调料)
- 糖 3g (调料)
- 食用油 10g (调料)
- 大蒜 4-5瓣 (调料)
- 小米辣 1-2根 (调料)
- 清水 100g (辅料)

**制作步骤：**
1. 菜心洗净，去除根部比较硬或老的地方...
2. 大蒜切成蒜末，有洋葱顺便加了点洋葱
3. 调制灵魂料汁：生抽5g、蚝油5g，加3g糖和100g清水半碗成一碗汁儿
4. 一锅500ml清水加5g盐和10g食用油烧开
5. 将菜心根茎在沸水中烫1分钟，直到根茎颜色变成深绿。再将整个菜心放到锅中烫熟1分钟，捞起来码入盘中 (120秒)
6. 开另一小锅将兑好的料汁倒入，小火烧开...
7. 料汁稍微收汁，煮沸后稍等十来秒，后直接浇在菜心上... (10秒)

## API接口

### 1. 解析GitHub菜谱

#### 接口地址
```
POST /recipe-parser/parse-github
```

#### 请求参数
```
githubUrl: GitHub文件URL (必填)
```

#### 示例请求
```bash
curl -X POST "http://localhost:8080/recipe-parser/parse-github" \
  -H "Authorization: Bearer your_jwt_token" \
  -d "githubUrl=https://github.com/Anduin2017/HowToCook/blob/master/dishes/vegetable_dish/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83.md"
```

#### 响应数据
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "name": "白灼菜心",
    "description": "白灼菜心是经典粤菜...",
    "difficultyLevel": 2,
    "servings": 2,
    "ingredients": [
      {
        "name": "菜心",
        "quantity": "250g",
        "type": "main"
      }
    ],
    "steps": [
      {
        "description": "菜心洗净，去除根部比较硬或老的地方...",
        "durationSeconds": null
      }
    ]
  }
}
```

### 2. 解析并保存GitHub菜谱到数据库

#### 接口地址
```
POST /recipe-parser/parse-and-save-github
```

#### 请求参数
```
githubUrl: GitHub文件URL (必填)
```

#### 响应数据
```json
{
  "code": 200,
  "msg": "success",
  "data": 1
}
```
返回的data是保存后的菜谱ID。

### 3. 解析Markdown内容

#### 接口地址
```
POST /recipe-parser/parse-markdown
```

#### 请求参数
```json
{
  "markdownContent": "# 菜谱名称的做法\n\n预估烹饪难度：★★\n\n## 必备原料和工具\n..."
}
```

#### 响应数据
同解析GitHub菜谱接口。

### 4. 解析示例菜谱

#### 接口地址
```
GET /recipe-parser/parse-example
```

#### 功能说明
解析白灼菜心示例菜谱，用于测试和演示。

### 5. 保存示例菜谱到数据库

#### 接口地址
```
POST /recipe-parser/save-example
```

#### 功能说明
解析白灼菜心示例菜谱并保存到数据库。

## 支持的Markdown格式

解析器支持以下Markdown格式的菜谱：

### 标题格式
```markdown
# 菜谱名称的做法
```

### 难度等级
```markdown
预估烹饪难度：★★
```

### 食材列表
```markdown
## 计算
- 食材名称 数量
- 新鲜菜心 250g
- 生抽 5g
```

或者：
```markdown
## 必备原料和工具
- 新鲜菜心
- 生抽、蚝油、盐
```

### 制作步骤
```markdown
## 操作
1. 第一步操作描述
2. 第二步操作描述，烫1分钟
3. 第三步操作描述
```

## 使用场景

1. **菜谱导入**: 从开源菜谱库导入菜谱到系统
2. **内容管理**: 批量处理Markdown格式的菜谱文件
3. **数据迁移**: 将外部菜谱数据转换为系统格式
4. **API集成**: 与其他菜谱平台进行数据交换

## 注意事项

1. **网络访问**: 解析GitHub菜谱需要网络访问权限
2. **格式要求**: Markdown格式需要符合特定的结构
3. **编码问题**: 确保Markdown文件使用UTF-8编码
4. **权限验证**: 所有接口都需要JWT token验证
5. **重复检查**: 系统会自动处理重复的食材名称

## 扩展功能

1. **批量导入**: 支持批量导入多个菜谱文件
2. **格式验证**: 在解析前验证Markdown格式
3. **图片处理**: 提取菜谱中的图片链接
4. **标签提取**: 自动提取菜谱标签和分类
5. **营养分析**: 基于食材计算营养信息
