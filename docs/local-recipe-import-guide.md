# 本地菜谱导入功能使用指南

## 概述

系统已升级支持从本地HowToCook项目文件夹批量导入菜谱，并新增了菜品分类功能。

## 新增功能

### 1. 菜品分类字段
数据库表`cook_recipe`新增`category`字段：
```sql
ALTER TABLE cook_recipe 
ADD category INT DEFAULT 0 NOT NULL 
COMMENT '菜品分类：0-未知 1-荤菜 2-蔬菜 3-汤 4-海鲜 5-甜点 6-饮品 7-主食 8-调料 9-早餐';
```

### 2. 分类枚举管理
创建了`RecipeCategoryEnum`枚举类，管理菜品分类：

| 代码 | 名称 | 文件夹名称 |
|------|------|------------|
| 0 | 未知 | unknown |
| 1 | 荤菜 | meat_dish |
| 2 | 蔬菜 | vegetable_dish |
| 3 | 汤 | soup |
| 4 | 海鲜 | seafood |
| 5 | 甜点 | dessert |
| 6 | 饮品 | beverage |
| 7 | 主食 | staple_food |
| 8 | 调料 | seasoning |
| 9 | 早餐 | breakfast |

### 3. 本地文件导入服务
- `LocalRecipeImportService` - 本地文件解析和导入
- 支持递归扫描文件夹
- 自动识别分类
- 批量导入统计

## 配置设置

### application.yml配置
```yaml
recipe:
  import:
    # HowToCook项目的dishes文件夹路径
    base-path: /Users/<USER>/Desktop/HowToCook-master/dishes
```

### 文件夹结构要求
```
/Users/<USER>/Desktop/HowToCook-master/dishes/
├── vegetable_dish/          # 蔬菜类
│   ├── 白灼菜心/
│   │   └── 白灼菜心.md
│   └── 其他蔬菜菜谱.md
├── meat_dish/               # 荤菜类
│   └── 各种荤菜.md
├── soup/                    # 汤类
├── seafood/                 # 海鲜类
├── dessert/                 # 甜点类
├── beverage/                # 饮品类
├── staple_food/             # 主食类
├── seasoning/               # 调料类
└── breakfast/               # 早餐类
```

## API接口

### 1. 单个菜谱导入
```bash
POST /recipe-parser/parse-and-save-local
```

**参数：**
- `categoryFolderName`: 分类文件夹名称（如：vegetable_dish）
- `fileName`: 菜谱文件名（如：白灼菜心.md）

**示例：**
```bash
curl -X POST "http://localhost:8080/recipe-parser/parse-and-save-local" \
  -H "Authorization: Bearer your_jwt_token" \
  -d "categoryFolderName=vegetable_dish&fileName=白灼菜心.md"
```

**响应：**
```json
{
  "code": 200,
  "msg": "success",
  "data": 1
}
```

### 2. 批量导入所有菜谱
```bash
POST /recipe-parser/batch-import-local
```

**示例：**
```bash
curl -X POST "http://localhost:8080/recipe-parser/batch-import-local" \
  -H "Authorization: Bearer your_jwt_token"
```

**响应：**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "totalSuccess": 25,
    "totalFailed": 2,
    "categoryResults": [
      {
        "categoryName": "蔬菜",
        "successCount": 10,
        "failedCount": 1,
        "successFiles": ["白灼菜心.md -> 白灼菜心 (ID:1)"],
        "failedFiles": ["错误文件.md"],
        "failedReasons": ["解析失败"]
      }
    ]
  }
}
```

### 3. 按分类搜索菜谱
```bash
POST /recipe/search
```

**参数：**
```json
{
  "category": 2,
  "pageNum": 1,
  "pageSize": 10
}
```

## 使用步骤

### 1. 准备HowToCook项目
```bash
# 下载HowToCook项目
git clone https://github.com/Anduin2017/HowToCook.git
cd HowToCook

# 确认dishes文件夹结构
ls dishes/
```

### 2. 更新配置
修改`application.yml`中的路径：
```yaml
recipe:
  import:
    base-path: /path/to/your/HowToCook/dishes
```

### 3. 执行数据库更新
```sql
-- 添加category字段
ALTER TABLE cook_recipe 
ADD category INT DEFAULT 0 NOT NULL 
COMMENT '菜品分类：0-未知 1-荤菜 2-蔬菜 3-汤 4-海鲜 5-甜点 6-饮品 7-主食 8-调料 9-早餐';
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 执行导入
```bash
# 测试单个菜谱导入
curl -X POST "http://localhost:8080/recipe-parser/parse-and-save-local" \
  -H "Authorization: Bearer your_jwt_token" \
  -d "categoryFolderName=vegetable_dish&fileName=白灼菜心.md"

# 批量导入所有菜谱
curl -X POST "http://localhost:8080/recipe-parser/batch-import-local" \
  -H "Authorization: Bearer your_jwt_token"
```

### 6. 验证结果
```bash
# 运行测试脚本
./scripts/test-local-import.sh your_jwt_token

# 查看导入统计
curl -X POST "http://localhost:8080/recipe/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{"category": 2, "pageNum": 1, "pageSize": 10}'
```

## 导入流程

### 1. 文件扫描
- 递归扫描指定路径下的所有文件夹
- 根据文件夹名称识别菜品分类
- 查找所有`.md`文件

### 2. 内容解析
- 读取Markdown文件内容（UTF-8编码）
- 使用`MarkdownRecipeParser`解析菜谱信息
- 自动设置菜品分类

### 3. 数据保存
- 创建或查找食材记录
- 保存菜谱基本信息（包含分类）
- 建立菜谱-食材关联
- 保存制作步骤

### 4. 结果统计
- 按分类统计成功/失败数量
- 记录失败原因
- 生成详细报告

## 错误处理

### 常见错误及解决方案

1. **路径不存在**
   ```
   错误：基础路径不存在或不是目录
   解决：检查application.yml中的base-path配置
   ```

2. **文件编码问题**
   ```
   错误：文件内容乱码
   解决：确保Markdown文件使用UTF-8编码
   ```

3. **解析失败**
   ```
   错误：菜谱解析失败或菜名为空
   解决：检查Markdown文件格式是否符合要求
   ```

4. **重复菜谱**
   ```
   错误：菜谱名称已存在
   解决：系统会自动跳过重复菜谱
   ```

## 性能优化

### 1. 批量导入建议
- 首次导入建议分批进行
- 监控内存使用情况
- 大量数据导入时考虑增加JVM内存

### 2. 数据库优化
- 为category字段添加索引
- 定期清理失败的导入记录
- 监控数据库连接池

### 3. 文件处理优化
- 跳过非Markdown文件
- 缓存已处理的文件列表
- 支持增量导入

## 监控和日志

### 日志级别
```yaml
logging:
  level:
    com.cook.service.LocalRecipeImportService: DEBUG
```

### 关键日志
- 文件扫描进度
- 解析成功/失败记录
- 数据库保存结果
- 性能统计信息

现在系统已完全支持本地文件导入，可以从HowToCook项目批量导入菜谱并自动分类！
