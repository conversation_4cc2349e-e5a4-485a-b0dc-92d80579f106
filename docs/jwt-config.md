# JWT配置说明

## 1. JWT配置参数

在 `application.yml` 中的JWT相关配置：

```yaml
jwt:
  # JWT密钥，生产环境请使用更复杂的密钥
  secret: cook_jwt_secret_key_2025_very_long_secret_key_for_security_please_change_in_production
  # JWT过期时间（毫秒）7天 = 7 * 24 * 60 * 60 * 1000
  expiration: 604800000
  # JWT签发者
  issuer: cook-app
  # JWT请求头名称
  header: Authorization
  # JWT token前缀
  token-prefix: "Bearer "
```

## 2. 配置参数说明

| 参数 | 说明 | 默认值 | 建议 |
|------|------|--------|------|
| secret | JWT签名密钥 | cook_jwt_secret_key_2025... | 生产环境必须修改为复杂密钥 |
| expiration | token过期时间（毫秒） | 604800000 (7天) | 根据业务需求调整 |
| issuer | JWT签发者标识 | cook-app | 可保持默认或修改为应用名 |
| header | HTTP请求头名称 | Authorization | 建议保持默认 |
| token-prefix | token前缀 | "Bearer " | 建议保持默认 |

## 3. 安全建议

### 3.1 密钥安全
- **生产环境必须修改密钥**：默认密钥仅用于开发测试
- **密钥长度**：建议至少64个字符
- **密钥复杂度**：包含大小写字母、数字、特殊字符
- **密钥保密**：不要将密钥提交到版本控制系统

### 3.2 过期时间设置
- **移动端应用**：建议7-30天
- **Web应用**：建议1-7天
- **高安全要求**：建议1-24小时

### 3.3 环境变量配置
生产环境建议使用环境变量：

```yaml
jwt:
  secret: ${JWT_SECRET:default_secret}
  expiration: ${JWT_EXPIRATION:604800000}
```

## 4. JWT Token结构

### 4.1 Header（头部）
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

### 4.2 Payload（载荷）
```json
{
  "userId": 123,
  "openid": "wx_openid_123",
  "nickname": "用户昵称",
  "sub": "123",
  "iss": "cook-app",
  "iat": 1640995200,
  "exp": 1641600000
}
```

### 4.3 Signature（签名）
使用HMAC SHA256算法，基于header、payload和secret生成。

## 5. 拦截器配置

### 5.1 需要验证的路径
- `/cook/**` - 所有菜谱相关接口
- `/user/info/**` - 用户信息接口

### 5.2 排除验证的路径
- `/user/wx-login` - 微信登录
- `/user/refresh-token/**` - 刷新token
- `/static/**` - 静态资源
- `/error` - 错误页面
- `/actuator/**` - 健康检查

## 6. 错误处理

### 6.1 常见错误码
- `401 Unauthorized` - token无效或过期
- `403 Forbidden` - 权限不足
- `400 Bad Request` - 请求格式错误

### 6.2 错误响应格式
```json
{
  "code": 401,
  "msg": "访问令牌无效或已过期",
  "data": null
}
```

## 7. 开发调试

### 7.1 JWT在线解析工具
- https://jwt.io/
- 可以解析token内容，验证签名

### 7.2 测试token生成
```bash
# 使用测试接口生成token
curl -X POST http://localhost:8080/user/wx-login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "test_code",
    "userInfo": {
      "nickName": "测试用户",
      "avatarUrl": "https://example.com/avatar.jpg"
    }
  }'
```

### 7.3 测试token验证
```bash
# 使用token访问受保护接口
curl -X GET http://localhost:8080/user/info/123 \
  -H "Authorization: Bearer your_jwt_token_here"
```

## 8. 性能优化

### 8.1 Token缓存
- 可以考虑将有效token缓存到Redis
- 支持token黑名单机制

### 8.2 异步验证
- 对于高并发场景，可以考虑异步验证
- 使用线程池处理token验证

## 9. 监控和日志

### 9.1 关键日志
- token生成日志
- token验证失败日志
- token过期日志

### 9.2 监控指标
- token生成频率
- token验证成功率
- token过期率

## 10. 升级和维护

### 10.1 密钥轮换
- 定期更换JWT密钥
- 支持多密钥验证（向后兼容）

### 10.2 算法升级
- 当前使用HS256算法
- 可升级到RS256（非对称加密）
