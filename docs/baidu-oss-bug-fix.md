# 百度OSS服务BUG修复说明

## 问题描述

在调用`BaiduOssService#generateUrl`方法时出现以下错误：

### 1. Invalid Expires:0 错误
```
WARN com.baidubce.http.BceHttpResponse - Invalid Expires:0
java.lang.IllegalArgumentException: Invalid format: "0"
```

### 2. InaccessibleObjectException 错误
```
java.lang.reflect.InaccessibleObjectException: Unable to make synchronized java.net.InetAddress java.net.URL.getHostAddress() accessible: module java.base does not "opens java.net" to unnamed module
```

## 问题分析

### 1. Invalid Expires:0 问题
- **原因**: STS Token请求时没有设置有效的`durationSeconds`参数
- **影响**: 导致生成的Token过期时间为0，无法正常使用

### 2. InaccessibleObjectException 问题
- **原因**: Java 9+模块系统限制了对`java.net`包的反射访问
- **影响**: HuTool的JSONUtil在序列化URL对象时失败

## 修复方案

### 1. 修复STS Token过期时间问题

**修复前：**
```java
GetSessionTokenResponse response = stsClient.getSessionToken(new GetSessionTokenRequest());
```

**修复后：**
```java
GetSessionTokenRequest request = new GetSessionTokenRequest();
request.setDurationSeconds(3600); // 设置STS Token有效期为1小时
GetSessionTokenResponse response = stsClient.getSessionToken(request);
```

### 2. 修复URL序列化问题

**修复前：**
```java
URL url = bosClient.generatePresignedUrl(bucketName, objectKey, 86400);
log.info("文件访问地址：{}", JSONUtil.toJsonStr(url)); // 这里会出错
return url.toString();
```

**修复后：**
```java
URL url = bosClient.generatePresignedUrl(bucketName, objectKey, 3600);
String urlString = url.toString();
log.info("文件访问地址生成成功，文件ID：{}，URL长度：{}", id, urlString.length());
return urlString;
```

### 3. 添加资源管理

**修复后的完整方法：**
```java
public String generateUrl(long id) {
    String objectKey = getObjectKey(id);
    BosClient bosClient = null;
    
    try {
        // 获取STS Token，设置有效期
        StsClient stsClient = baiduOssClientConfig.getStsClient();
        GetSessionTokenRequest request = new GetSessionTokenRequest();
        request.setDurationSeconds(3600); // 设置STS Token有效期为1小时
        GetSessionTokenResponse response = stsClient.getSessionToken(request);

        // 生成BOS客户端
        BceCredentials bosstsCredentials = new DefaultBceSessionCredentials(
                response.getAccessKeyId(),
                response.getSecretAccessKey(),
                response.getSessionToken());
        BosClientConfiguration config = new BosClientConfiguration();
        config.setCredentials(bosstsCredentials);
        bosClient = new BosClient(config);

        // 生成预签名URL，有效期1小时
        URL url = bosClient.generatePresignedUrl(baiduOssClientConfig.getOssBucketName(), objectKey, 3600);
        String urlString = url.toString();
        log.info("文件访问地址生成成功，文件ID：{}，URL长度：{}", id, urlString.length());
        return urlString;
        
    } catch (Exception e) {
        log.error("生成文件访问地址失败，文件ID：{}", id, e);
        throw new RuntimeException("生成文件访问地址失败：" + e.getMessage(), e);
    } finally {
        // 确保客户端被正确关闭
        if (bosClient != null) {
            try {
                bosClient.shutdown();
            } catch (Exception e) {
                log.warn("关闭BOS客户端失败", e);
            }
        }
    }
}
```

## 修复的控制器问题

### 1. 修复文件上传方法

**修复前：**
```java
long fileId = baiduOssService.uploadFile(file.getInputStream());
```

**修复后：**
```java
File tempFile = convertToFile(file);
try {
    long fileId = baiduOssService.uploadFile(tempFile);
    // ... 处理结果
} finally {
    // 清理临时文件
    if (tempFile != null && tempFile.exists()) {
        boolean deleted = tempFile.delete();
        if (!deleted) {
            log.warn("临时文件删除失败：{}", tempFile.getAbsolutePath());
        }
    }
}
```

### 2. 修复方法调用

**修复前：**
```java
String fileUrl = baiduOssService.generateUrl("bos:/version-cook/", fileId);
```

**修复后：**
```java
String fileUrl = baiduOssService.generateUrl(fileId);
```

## 修复效果

### 1. 解决的问题
- ✅ STS Token过期时间设置正确
- ✅ URL生成不再出现序列化错误
- ✅ 资源正确释放，避免内存泄漏
- ✅ 临时文件正确清理

### 2. 性能改进
- **URL有效期**: 从24小时调整为1小时，提高安全性
- **资源管理**: 添加了BOS客户端的正确关闭
- **错误处理**: 更详细的错误信息和日志

### 3. 安全性提升
- **Token有效期**: STS Token有效期设置为1小时
- **URL有效期**: 预签名URL有效期设置为1小时
- **资源清理**: 确保临时文件和客户端资源被正确释放

## 测试验证

### 1. 单元测试
```bash
# 测试文件上传
curl -X POST "http://localhost:8080/baidu-oss/upload" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@test.jpg"

# 测试URL获取
curl -X GET "http://localhost:8080/baidu-oss/url/1234567890" \
  -H "Authorization: Bearer your_jwt_token"
```

### 2. 日志验证
修复后的日志应该显示：
```
INFO com.cook.service.client.BaiduOssService - 文件访问地址生成成功，文件ID：1234567890，URL长度：256
INFO com.cook.controller.BaiduOssController - 文件上传成功，ID：1234567890，URL：https://...
```

而不是之前的错误：
```
WARN com.baidubce.http.BceHttpResponse - Invalid Expires:0
ERROR com.cook.controller.BaiduOssController - 文件上传失败
```

## 预防措施

### 1. 配置验证
确保百度OSS相关配置正确：
```yaml
baidu:
  oss:
    endpoint: https://bos.baidubce.com
    access-key: your_access_key
    secret-key: your_secret_key
    bucket: your_bucket_name
```

### 2. 监控建议
- 监控STS Token获取成功率
- 监控URL生成成功率
- 监控临时文件清理情况
- 监控BOS客户端资源使用

### 3. 错误处理
- 添加重试机制（可选）
- 详细的错误日志记录
- 优雅的降级策略

## 后续优化建议

### 1. 缓存机制
```java
// 可以考虑缓存STS Token，避免频繁请求
private final Cache<String, GetSessionTokenResponse> tokenCache = 
    Caffeine.newBuilder()
        .expireAfterWrite(50, TimeUnit.MINUTES) // 提前10分钟过期
        .build();
```

### 2. 连接池
```java
// 使用连接池管理BOS客户端
private final BosClient sharedBosClient = createSharedBosClient();
```

### 3. 异步处理
```java
// 对于大文件上传，可以考虑异步处理
@Async
public CompletableFuture<String> uploadFileAsync(File file) {
    // 异步上传逻辑
}
```

现在百度OSS服务的BUG已经完全修复，可以正常使用文件上传和URL生成功能了！
