# 描述提取功能测试说明

## 功能概述

已优化`MarkdownRecipeParser#extractDescription`方法，现在可以正确过滤以下内容：

1. **HTML注释** - `<!-- ... -->`
2. **图片链接** - `![图片](./path.jpg)`
3. **引用块** - `> 引用内容`
4. **Markdown格式标记** - `**粗体**`、`*斜体*`、`[链接](url)`等

## 测试用例

### 原始Markdown内容
```markdown
# 白灼菜心的做法

<!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->

![白灼菜心](./白灼菜心.jpg)

> 没有拍照，上图是网图，不过做出来都差不多啦

白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。

总之吧，减肥或者是**快速解决绿叶菜的绝佳方式**。

预估烹饪难度：★★

## 必备原料和工具
```

### 期望的提取结果
```
白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。 总之吧，减肥或者是快速解决绿叶菜的绝佳方式。
```

## 过滤规则

### 1. HTML注释过滤
- **过滤内容**: `<!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->`
- **正则表达式**: `<!--[\\s\\S]*?-->`
- **结果**: 完全移除

### 2. 图片链接过滤
- **过滤内容**: `![白灼菜心](./白灼菜心.jpg)`
- **检测规则**: 以`![`开头或包含`![...](...)` 模式
- **结果**: 跳过整行

### 3. 引用块过滤
- **过滤内容**: `> 没有拍照，上图是网图，不过做出来都差不多啦`
- **检测规则**: 以`>`开头
- **结果**: 跳过整行

### 4. Markdown格式清理
- **粗体**: `**快速解决绿叶菜的绝佳方式**` → `快速解决绿叶菜的绝佳方式`
- **斜体**: `*文字*` → `文字`
- **代码**: `` `代码` `` → `代码`
- **链接**: `[文字](url)` → `文字`

## 测试方法

### 方法1: API接口测试
```bash
curl -X GET "http://localhost:8080/recipe-parser/test-description" \
  -H "Authorization: Bearer your_jwt_token"
```

### 方法2: 直接解析测试
```bash
curl -X POST "http://localhost:8080/recipe-parser/parse-markdown" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "markdownContent": "# 白灼菜心的做法\n\n<!-- 注释 -->\n\n![图片](./image.jpg)\n\n> 引用\n\n这是正常描述。\n\n## 下一章节"
  }'
```

### 方法3: 完整流程测试
```bash
# 1. 解析并保存
curl -X POST "http://localhost:8080/recipe-parser/save-baizhuocaixin" \
  -H "Authorization: Bearer your_jwt_token"

# 2. 查看结果（假设返回ID为1）
curl -X GET "http://localhost:8080/recipe/detail/1" \
  -H "Authorization: Bearer your_jwt_token"
```

## 验证要点

### ✅ 应该包含的内容
- "白灼菜心是经典粤菜"
- "快速解决绿叶菜的绝佳方式"
- 正常的描述性文字

### ❌ 不应该包含的内容
- HTML注释内容
- 图片文件名或路径
- 引用块中的内容
- Markdown格式标记（`**`、`*`、`[]`等）

## 代码实现

### 核心方法
```java
private String extractDescription(String content) {
    // 1. 清理HTML注释
    content = removeHtmlComments(content);
    
    // 2. 逐行处理
    String[] lines = content.split("\n");
    StringBuilder description = new StringBuilder();
    boolean foundTitle = false;
    
    for (String line : lines) {
        line = line.trim();
        
        if (line.startsWith("#")) {
            foundTitle = true;
            continue;
        }
        
        if (foundTitle && StrUtil.isNotBlank(line)) {
            if (shouldSkipLine(line)) {
                continue;
            }
            
            if (line.startsWith("##")) {
                break;
            }
            
            String cleanedLine = cleanDescriptionLine(line);
            if (StrUtil.isNotBlank(cleanedLine)) {
                description.append(cleanedLine).append(" ");
                if (description.length() > 300) break;
            }
        }
    }
    
    return description.toString().trim();
}
```

### 辅助方法
- `removeHtmlComments()` - 移除HTML注释
- `shouldSkipLine()` - 判断是否跳过行
- `cleanDescriptionLine()` - 清理行内Markdown格式

## 测试结果示例

### 输入
```markdown
# 白灼菜心的做法

<!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->

![白灼菜心](./白灼菜心.jpg)

> 没有拍照，上图是网图，不过做出来都差不多啦

白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。

总之吧，减肥或者是**快速解决绿叶菜的绝佳方式**。
```

### 输出
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "name": "白灼菜心",
    "description": "白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。 总之吧，减肥或者是快速解决绿叶菜的绝佳方式。",
    "difficultyLevel": 2,
    "servings": 2
  }
}
```

## 注意事项

1. **长度限制**: 描述内容限制在300字符以内
2. **格式清理**: 自动移除所有Markdown格式标记
3. **空行处理**: 自动跳过空行和只包含特殊字符的行
4. **章节分割**: 遇到`##`标题时停止提取描述
