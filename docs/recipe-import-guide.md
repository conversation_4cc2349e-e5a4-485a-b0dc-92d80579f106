# 菜谱导入使用指南

## 概述

本系统支持从GitHub上的Markdown格式菜谱文件中解析并导入菜谱数据到数据库中。已成功解析并测试了白灼菜心菜谱。

## 数据库表结构

系统使用以下4个表存储菜谱数据：

### 1. cook_recipe (菜谱表)
- `id`: 菜谱ID (主键)
- `name`: 菜名
- `description`: 简介
- `difficulty_level`: 难度等级 (1-3)
- `servings`: 份数

### 2. cook_ingredient (食材表)
- `id`: 食材ID (主键)
- `name`: 食材名称 (唯一约束)

### 3. cook_recipe_ingredient (菜谱-食材映射表)
- `id`: 映射ID (主键)
- `recipe_id`: 菜谱ID
- `ingredient_id`: 食材ID
- `quantity`: 数量 (如"250g", "2个")
- `type`: 类型 (main/supplement/seasoning)

### 4. cook_recipe_step (制作步骤表)
- `id`: 步骤ID (主键)
- `recipe_id`: 菜谱ID
- `step_order`: 步骤顺序
- `description`: 步骤描述
- `duration_seconds`: 持续时间(秒)

## 白灼菜心解析结果

### 基本信息
- **菜名**: 白灼菜心
- **难度**: 2 (★★)
- **份数**: 2人份
- **描述**: 白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法...

### 食材列表 (9种)
| 食材名称 | 数量 | 类型 |
|---------|------|------|
| 菜心 | 250g | main |
| 生抽 | 5g | seasoning |
| 蚝油 | 5g | seasoning |
| 盐 | 5g | seasoning |
| 糖 | 3g | seasoning |
| 食用油 | 10g | seasoning |
| 大蒜 | 4-5瓣 | seasoning |
| 小米辣 | 1-2根 | seasoning |
| 清水 | 100g | supplement |

### 制作步骤 (7步)
1. 菜心洗净，去除根部比较硬或老的地方...
2. 大蒜切成蒜末，有洋葱顺便加了点洋葱
3. 调制灵魂料汁：生抽5g、蚝油5g，加3g糖和100g清水半碗成一碗汁儿
4. 一锅500ml清水加5g盐和10g食用油烧开
5. 将菜心根茎在沸水中烫1分钟... (120秒)
6. 开另一小锅将兑好的料汁倒入，小火烧开...
7. 料汁稍微收汁，煮沸后稍等十来秒... (10秒)

## 使用步骤

### 1. 初始化数据库
```sql
-- 执行初始化脚本
SOURCE src/main/resources/sql/init_recipe_tables.sql;
```

### 2. 启动应用
```bash
mvn spring-boot:run
```

### 3. 调用API导入菜谱

#### 方法一：导入白灼菜心示例
```bash
curl -X POST "http://localhost:8080/recipe-parser/save-baizhuocaixin" \
  -H "Authorization: Bearer your_jwt_token"
```

#### 方法二：从GitHub导入
```bash
curl -X POST "http://localhost:8080/recipe-parser/parse-and-save-github" \
  -H "Authorization: Bearer your_jwt_token" \
  -d "githubUrl=https://github.com/Anduin2017/HowToCook/blob/master/dishes/vegetable_dish/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83.md"
```

#### 方法三：直接提交Markdown内容
```bash
curl -X POST "http://localhost:8080/recipe-parser/parse-markdown" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "markdownContent": "# 菜谱名称的做法\n\n预估烹饪难度：★★\n\n## 计算\n- 食材 数量\n\n## 操作\n1. 步骤描述"
  }'
```

### 4. 验证导入结果

#### 查询菜谱详情
```bash
curl -X GET "http://localhost:8080/recipe/detail/{recipeId}" \
  -H "Authorization: Bearer your_jwt_token"
```

#### 搜索菜谱
```bash
curl -X POST "http://localhost:8080/recipe/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "recipeName": "白灼菜心",
    "pageNum": 1,
    "pageSize": 10
  }'
```

## API接口说明

### 解析相关接口
- `POST /recipe-parser/parse-github` - 解析GitHub菜谱
- `POST /recipe-parser/parse-markdown` - 解析Markdown内容
- `POST /recipe-parser/parse-and-save-github` - 解析并保存GitHub菜谱
- `POST /recipe-parser/save-baizhuocaixin` - 保存白灼菜心示例
- `GET /recipe-parser/parse-example` - 解析示例菜谱

### 查询相关接口
- `POST /recipe/search` - 搜索菜谱
- `GET /recipe/detail/{recipeId}` - 获取菜谱详情
- `GET /recipe/search-by-ingredients` - 根据食材搜索菜谱

## 响应示例

### 成功导入响应
```json
{
  "code": 200,
  "msg": "success",
  "data": 1
}
```

### 菜谱详情响应
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "白灼菜心",
    "description": "白灼菜心是经典粤菜...",
    "difficultyLevel": 2,
    "difficultyLevelDesc": "中等",
    "servings": 2,
    "ingredients": [
      {
        "ingredientId": 1,
        "ingredientName": "菜心",
        "quantity": "250g",
        "type": "main",
        "typeDesc": "主料"
      }
    ],
    "steps": [
      {
        "id": 1,
        "stepOrder": 1,
        "description": "菜心洗净，去除根部比较硬或老的地方...",
        "durationSeconds": null,
        "durationDesc": null
      }
    ]
  }
}
```

## 注意事项

1. **权限验证**: 所有接口都需要JWT token验证
2. **数据库初始化**: 首次使用需要执行初始化SQL脚本
3. **网络访问**: GitHub解析需要网络连接
4. **重复处理**: 系统会自动处理重复的食材名称
5. **事务处理**: 导入过程使用事务，失败会自动回滚

## 扩展功能

1. **批量导入**: 可以扩展支持批量导入多个菜谱
2. **格式验证**: 可以添加Markdown格式验证
3. **图片处理**: 可以提取和处理菜谱图片
4. **分类管理**: 可以添加菜谱分类功能
5. **营养分析**: 可以基于食材计算营养信息

## 故障排除

1. **导入失败**: 检查数据库连接和表结构
2. **解析错误**: 检查Markdown格式是否符合要求
3. **网络问题**: 使用本地Markdown内容而非GitHub URL
4. **权限问题**: 确保JWT token有效
5. **重复数据**: 系统会自动处理，不会重复插入相同食材
