# 百度OSS文件存储API文档

## 概述

基于现有的`BaiduOssService`设计了3个RESTful接口，用于文件上传和文件地址获取功能。

## API接口

### 1. 文件上传接口

#### 接口地址
```
POST /baidu-oss/upload
```

#### 请求参数
- **Content-Type**: `multipart/form-data`
- **file**: 要上传的文件（必填）

#### 文件限制
- **文件大小**: 最大10MB
- **支持格式**: 
  - 图片：`image/*`
  - 视频：`video/*`
  - 文档：`application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - 文本：`text/plain`

#### 请求示例
```bash
curl -X POST "http://localhost:8080/baidu-oss/upload" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@/path/to/your/file.jpg"
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "fileId": 1234567890,
    "fileName": "example.jpg",
    "fileUrl": "https://bos.baidubce.com/version-cook/photos/1234567890",
    "fileSize": 1024000,
    "contentType": "image/jpeg"
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "msg": "文件大小不能超过10MB",
  "data": null
}
```

### 2. 获取文件访问URL接口

#### 接口地址
```
GET /baidu-oss/url/{fileId}
```

#### 路径参数
- **fileId**: 文件ID（必填，Long类型）

#### 请求示例
```bash
curl -X GET "http://localhost:8080/baidu-oss/url/1234567890" \
  -H "Authorization: Bearer your_jwt_token"
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "fileId": 1234567890,
    "fileUrl": "https://bos.baidubce.com/version-cook/photos/1234567890?authorization=xxx",
    "expireTime": 1640995200000
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "msg": "文件ID无效",
  "data": null
}
```

### 3. 批量获取文件访问URL接口

#### 接口地址
```
GET /baidu-oss/urls
```

#### 查询参数
- **fileIds**: 文件ID列表，逗号分隔（必填）
- **限制**: 一次最多50个文件ID

#### 请求示例
```bash
curl -X GET "http://localhost:8080/baidu-oss/urls?fileIds=1234567890,1234567891,1234567892" \
  -H "Authorization: Bearer your_jwt_token"
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "fileUrls": [
      {
        "fileId": 1234567890,
        "fileUrl": "https://bos.baidubce.com/version-cook/photos/1234567890?authorization=xxx",
        "success": true,
        "errorMessage": null
      },
      {
        "fileId": 1234567891,
        "fileUrl": "https://bos.baidubce.com/version-cook/photos/1234567891?authorization=xxx",
        "success": true,
        "errorMessage": null
      },
      {
        "fileId": null,
        "fileUrl": null,
        "success": false,
        "errorMessage": "无效的文件ID：invalid_id"
      }
    ],
    "successCount": 2,
    "failCount": 1
  }
}
```

## 使用场景

### 1. 菜谱图片上传
```javascript
// 前端上传菜谱图片
const formData = new FormData();
formData.append('file', imageFile);

fetch('/baidu-oss/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('上传成功，文件ID：', data.data.fileId);
    console.log('访问URL：', data.data.fileUrl);
  }
});
```

### 2. 获取菜谱图片URL
```javascript
// 根据文件ID获取访问URL
fetch(`/baidu-oss/url/${fileId}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('文件URL：', data.data.fileUrl);
  }
});
```

### 3. 批量获取图片URL
```javascript
// 批量获取多个文件的URL
const fileIds = [1234567890, 1234567891, 1234567892];
fetch(`/baidu-oss/urls?fileIds=${fileIds.join(',')}`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    data.data.fileUrls.forEach(item => {
      if (item.success) {
        console.log(`文件${item.fileId}的URL：${item.fileUrl}`);
      } else {
        console.error(`文件${item.fileId}获取失败：${item.errorMessage}`);
      }
    });
  }
});
```

## 技术实现

### 1. 基于现有服务
- 复用现有的`BaiduOssService`
- 使用现有的文件上传和URL生成逻辑
- 保持与现有系统的兼容性

### 2. 文件处理流程
1. **上传流程**：
   - 验证文件大小和类型
   - 转换MultipartFile为临时File
   - 调用BaiduOssService上传
   - 生成访问URL
   - 清理临时文件

2. **URL生成流程**：
   - 验证文件ID
   - 调用BaiduOssService生成预签名URL
   - 返回带过期时间的URL

### 3. 错误处理
- 文件大小限制（10MB）
- 文件类型验证
- 文件ID有效性检查
- 批量操作数量限制（50个）
- 详细的错误信息返回

### 4. 安全考虑
- JWT token验证
- 文件类型白名单
- 文件大小限制
- 临时文件自动清理

## 配置要求

### application.yml配置
```yaml
baidu:
  oss:
    endpoint: https://bos.baidubce.com
    access-key: your_access_key
    secret-key: your_secret_key
    bucket: version-cook
    domain: https://your-custom-domain.com
```

## 测试用例

### 1. 文件上传测试
```bash
# 上传图片文件
curl -X POST "http://localhost:8080/baidu-oss/upload" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@test.jpg"

# 上传PDF文件
curl -X POST "http://localhost:8080/baidu-oss/upload" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@document.pdf"
```

### 2. URL获取测试
```bash
# 获取单个文件URL
curl -X GET "http://localhost:8080/baidu-oss/url/1234567890" \
  -H "Authorization: Bearer your_jwt_token"

# 批量获取文件URL
curl -X GET "http://localhost:8080/baidu-oss/urls?fileIds=1,2,3,4,5" \
  -H "Authorization: Bearer your_jwt_token"
```

### 3. 错误场景测试
```bash
# 测试文件过大
curl -X POST "http://localhost:8080/baidu-oss/upload" \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@large_file.zip"

# 测试无效文件ID
curl -X GET "http://localhost:8080/baidu-oss/url/invalid_id" \
  -H "Authorization: Bearer your_jwt_token"

# 测试批量请求过多
curl -X GET "http://localhost:8080/baidu-oss/urls?fileIds=1,2,3,...,100" \
  -H "Authorization: Bearer your_jwt_token"
```

## 性能优化

### 1. 文件上传优化
- 使用临时文件避免内存占用
- 异步处理大文件上传
- 支持断点续传（可扩展）

### 2. URL生成优化
- 批量操作减少网络请求
- URL缓存机制（可扩展）
- 并发处理多个文件

### 3. 错误处理优化
- 详细的错误分类
- 部分成功的批量操作处理
- 优雅的降级策略

这套API设计充分利用了现有的BaiduOssService，提供了完整的文件上传和访问功能，同时考虑了安全性、性能和用户体验。
