# 百度OSS AccessDenied问题排查指南

## 问题现象

生成的URL访问时返回：
```json
{
  "code": "AccessDenied",
  "message": "Access Denied.",
  "requestId": "24577613-dbb7-4226-b51d-3bbb9abe2b21"
}
```

## 可能原因分析

### 1. STS权限问题
- STS Token权限不足
- STS策略配置错误
- STS Token过期

### 2. Bucket权限配置
- Bucket ACL设置错误
- 对象ACL设置错误
- 跨域访问限制

### 3. 签名问题
- 时间同步问题
- 签名算法错误
- 密钥配置错误

### 4. 网络和地域问题
- Endpoint配置错误
- 地域不匹配
- 网络访问限制

## 排查步骤

### 第一步：配置诊断

```bash
# 1. 检查配置和权限
curl -X GET "http://localhost:8080/baidu-oss/diagnose" \
  -H "Authorization: Bearer your_jwt_token"
```

**期望输出：**
```
百度OSS配置诊断：
1. 配置检查：
   - AccessKeyId: 已配置
   - SecretAccessKey: 已配置
   - Endpoint: https://bos.baidubce.com
   - BucketName: your-bucket-name
2. 连接检查：
   - Bucket连接：成功
   - Bucket中对象数量：10
3. STS检查：
   - STS Token获取：成功
   - STS AccessKeyId: AKIA...
```

### 第二步：文件存在性检查

```bash
# 2. 检查特定文件
curl -X GET "http://localhost:8080/baidu-oss/check/1234567890" \
  -H "Authorization: Bearer your_jwt_token"
```

**期望输出：**
```json
{
  "code": 200,
  "data": {
    "fileId": 1234567890,
    "exists": true,
    "fileSize": 1024000,
    "contentType": "image/jpeg",
    "lastModified": "2025-07-02T10:30:00Z",
    "fileUrl": "https://bos.baidubce.com/bucket/photos/1234567890?authorization=...",
    "urlError": null
  }
}
```

### 第三步：权限测试

```bash
# 3. 测试服务状态
curl -X GET "http://localhost:8080/baidu-oss/test" \
  -H "Authorization: Bearer your_jwt_token"
```

## 修复方案

### 方案1：使用主账号凭证（推荐）

**问题**: STS Token权限不足
**解决**: 直接使用主账号凭证生成预签名URL

```java
// 修复后的方法使用主账号凭证
public String generateUrl(long id) {
    // 直接使用主账号凭证，避免STS权限问题
    bosClient = baiduOssClientConfig.getOssClient();
    URL url = bosClient.generatePresignedUrl(bucketName, objectKey, 3600);
    return url.toString();
}
```

### 方案2：配置STS权限策略

如果必须使用STS，需要配置正确的权限策略：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "bos:GetObject",
        "bos:GetObjectMeta"
      ],
      "Resource": [
        "bos:your-bucket-name/*"
      ]
    }
  ]
}
```

### 方案3：检查Bucket权限

确保Bucket配置了正确的ACL：

```bash
# 检查Bucket ACL
curl -X GET "https://bos.baidubce.com/your-bucket-name?acl" \
  -H "Authorization: your-auth-header"
```

### 方案4：配置CORS（如果是浏览器访问）

```json
{
  "corsConfiguration": {
    "corsRule": [
      {
        "allowedOrigin": ["*"],
        "allowedMethod": ["GET", "HEAD"],
        "allowedHeader": ["*"],
        "maxAgeSeconds": 3600
      }
    ]
  }
}
```

## 配置检查清单

### 1. application.yml配置

```yaml
baidu:
  oss:
    secret-key-id: "your_access_key_id"
    access-key-secret: "your_secret_access_key"
    endpoint: "https://bos.baidubce.com"
    bucket-name: "your-bucket-name"
```

**注意事项：**
- ✅ 确保AccessKey和SecretKey正确
- ✅ 确保Endpoint格式正确（包含https://）
- ✅ 确保BucketName存在且有权限访问

### 2. 权限检查

**主账号权限：**
- ✅ 对目标Bucket有读取权限
- ✅ 对目标对象有读取权限
- ✅ 有生成预签名URL的权限

**STS权限（如果使用）：**
- ✅ STS策略包含bos:GetObject权限
- ✅ STS策略包含bos:GetObjectMeta权限
- ✅ 资源范围正确配置

### 3. 网络检查

```bash
# 测试网络连通性
curl -I "https://bos.baidubce.com"

# 测试Bucket访问
curl -I "https://bos.baidubce.com/your-bucket-name"
```

## 常见错误及解决方案

### 错误1: "SignatureDoesNotMatch"
**原因**: 签名计算错误
**解决**: 检查AccessKey和SecretKey是否正确

### 错误2: "InvalidBucketName"
**原因**: Bucket名称错误或不存在
**解决**: 检查Bucket名称配置

### 错误3: "NoSuchKey"
**原因**: 对象不存在
**解决**: 检查对象路径和上传状态

### 错误4: "RequestTimeTooSkewed"
**原因**: 服务器时间不同步
**解决**: 同步服务器时间

## 调试技巧

### 1. 启用详细日志

```yaml
logging:
  level:
    com.cook.service.client.BaiduOssService: DEBUG
    com.baidubce: DEBUG
```

### 2. 使用诊断接口

```bash
# 获取详细诊断信息
curl -X GET "http://localhost:8080/baidu-oss/diagnose" \
  -H "Authorization: Bearer your_jwt_token"
```

### 3. 手动测试URL

```bash
# 复制生成的URL，直接在浏览器或curl中测试
curl -I "生成的预签名URL"
```

### 4. 检查对象元数据

```bash
# 检查文件是否真实存在
curl -X GET "http://localhost:8080/baidu-oss/check/文件ID" \
  -H "Authorization: Bearer your_jwt_token"
```

## 最佳实践

### 1. 权限最小化原则
- 只授予必要的权限
- 使用资源级别的权限控制
- 定期审查权限配置

### 2. 安全考虑
- 使用HTTPS访问
- 设置合理的URL过期时间
- 避免在日志中暴露完整URL

### 3. 监控和告警
- 监控AccessDenied错误率
- 设置权限异常告警
- 定期检查配置有效性

### 4. 测试策略
- 定期运行诊断接口
- 自动化权限测试
- 监控URL生成成功率

通过以上排查步骤和修复方案，应该能够解决AccessDenied问题。如果问题仍然存在，建议联系百度云技术支持获取进一步帮助。
