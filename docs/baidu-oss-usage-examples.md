# 百度OSS文件存储使用示例

## 前端集成示例

### 1. JavaScript/Vue.js 文件上传组件

```vue
<template>
  <div class="file-upload">
    <input 
      type="file" 
      ref="fileInput" 
      @change="handleFileSelect"
      accept="image/*,video/*,.pdf,.doc,.docx,.txt"
    />
    <button @click="uploadFile" :disabled="!selectedFile || uploading">
      {{ uploading ? '上传中...' : '上传文件' }}
    </button>
    
    <div v-if="uploadResult" class="upload-result">
      <p>上传成功！</p>
      <p>文件ID: {{ uploadResult.fileId }}</p>
      <p>文件URL: <a :href="uploadResult.fileUrl" target="_blank">{{ uploadResult.fileUrl }}</a></p>
      <img v-if="isImage" :src="uploadResult.fileUrl" alt="上传的图片" style="max-width: 200px;" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedFile: null,
      uploading: false,
      uploadResult: null
    }
  },
  computed: {
    isImage() {
      return this.uploadResult && this.uploadResult.contentType.startsWith('image/')
    }
  },
  methods: {
    handleFileSelect(event) {
      this.selectedFile = event.target.files[0]
      this.uploadResult = null
    },
    
    async uploadFile() {
      if (!this.selectedFile) return
      
      this.uploading = true
      
      try {
        const formData = new FormData()
        formData.append('file', this.selectedFile)
        
        const response = await fetch('/baidu-oss/upload', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + this.$store.state.token
          },
          body: formData
        })
        
        const result = await response.json()
        
        if (result.code === 200) {
          this.uploadResult = result.data
          this.$message.success('文件上传成功')
        } else {
          this.$message.error('上传失败：' + result.msg)
        }
      } catch (error) {
        console.error('上传错误：', error)
        this.$message.error('上传失败：' + error.message)
      } finally {
        this.uploading = false
      }
    }
  }
}
</script>
```

### 2. React 文件上传组件

```jsx
import React, { useState } from 'react'
import { message } from 'antd'

const FileUpload = () => {
  const [selectedFile, setSelectedFile] = useState(null)
  const [uploading, setUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState(null)

  const handleFileSelect = (event) => {
    setSelectedFile(event.target.files[0])
    setUploadResult(null)
  }

  const uploadFile = async () => {
    if (!selectedFile) return

    setUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/baidu-oss/upload', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        body: formData
      })

      const result = await response.json()

      if (result.code === 200) {
        setUploadResult(result.data)
        message.success('文件上传成功')
      } else {
        message.error('上传失败：' + result.msg)
      }
    } catch (error) {
      console.error('上传错误：', error)
      message.error('上传失败：' + error.message)
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="file-upload">
      <input 
        type="file" 
        onChange={handleFileSelect}
        accept="image/*,video/*,.pdf,.doc,.docx,.txt"
      />
      <button onClick={uploadFile} disabled={!selectedFile || uploading}>
        {uploading ? '上传中...' : '上传文件'}
      </button>
      
      {uploadResult && (
        <div className="upload-result">
          <p>上传成功！</p>
          <p>文件ID: {uploadResult.fileId}</p>
          <p>文件URL: <a href={uploadResult.fileUrl} target="_blank" rel="noopener noreferrer">{uploadResult.fileUrl}</a></p>
          {uploadResult.contentType.startsWith('image/') && (
            <img src={uploadResult.fileUrl} alt="上传的图片" style={{maxWidth: '200px'}} />
          )}
        </div>
      )}
    </div>
  )
}

export default FileUpload
```

### 3. 批量获取文件URL的工具函数

```javascript
// 文件URL管理工具
class FileUrlManager {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl
    this.token = token
    this.urlCache = new Map() // URL缓存
  }

  // 获取单个文件URL
  async getFileUrl(fileId) {
    // 检查缓存
    if (this.urlCache.has(fileId)) {
      const cached = this.urlCache.get(fileId)
      // 检查是否过期（提前5分钟刷新）
      if (cached.expireTime > Date.now() + 5 * 60 * 1000) {
        return cached.url
      }
    }

    try {
      const response = await fetch(`${this.baseUrl}/baidu-oss/url/${fileId}`, {
        headers: {
          'Authorization': 'Bearer ' + this.token
        }
      })

      const result = await response.json()

      if (result.code === 200) {
        // 缓存URL
        this.urlCache.set(fileId, {
          url: result.data.fileUrl,
          expireTime: result.data.expireTime
        })
        return result.data.fileUrl
      } else {
        throw new Error(result.msg)
      }
    } catch (error) {
      console.error('获取文件URL失败：', error)
      return null
    }
  }

  // 批量获取文件URL
  async getBatchFileUrls(fileIds) {
    if (!fileIds || fileIds.length === 0) return {}

    // 分批处理，每批最多50个
    const batchSize = 50
    const results = {}

    for (let i = 0; i < fileIds.length; i += batchSize) {
      const batch = fileIds.slice(i, i + batchSize)
      
      try {
        const response = await fetch(`${this.baseUrl}/baidu-oss/urls?fileIds=${batch.join(',')}`, {
          headers: {
            'Authorization': 'Bearer ' + this.token
          }
        })

        const result = await response.json()

        if (result.code === 200) {
          result.data.fileUrls.forEach(item => {
            if (item.success) {
              results[item.fileId] = item.fileUrl
              // 缓存URL
              this.urlCache.set(item.fileId, {
                url: item.fileUrl,
                expireTime: Date.now() + 3600000 // 1小时后过期
              })
            }
          })
        }
      } catch (error) {
        console.error('批量获取文件URL失败：', error)
      }
    }

    return results
  }

  // 清理过期缓存
  cleanExpiredCache() {
    const now = Date.now()
    for (const [fileId, cached] of this.urlCache.entries()) {
      if (cached.expireTime <= now) {
        this.urlCache.delete(fileId)
      }
    }
  }
}

// 使用示例
const fileManager = new FileUrlManager('http://localhost:8080', 'your_jwt_token')

// 获取单个文件URL
const fileUrl = await fileManager.getFileUrl(1234567890)

// 批量获取文件URL
const fileUrls = await fileManager.getBatchFileUrls([1234567890, 1234567891, 1234567892])
```

## 后端集成示例

### 1. 菜谱图片管理服务

```java
@Service
@Slf4j
public class RecipeImageService {
    
    @Autowired
    private BaiduOssService baiduOssService;
    
    /**
     * 为菜谱上传图片
     */
    public String uploadRecipeImage(MultipartFile imageFile, Long recipeId) {
        try {
            // 验证图片格式
            if (!isValidImageFile(imageFile)) {
                throw new BusinessException("不支持的图片格式");
            }
            
            // 转换为临时文件
            File tempFile = convertToTempFile(imageFile);
            
            try {
                // 上传到百度OSS
                long fileId = baiduOssService.uploadFile(tempFile);
                
                // 生成访问URL
                String imageUrl = baiduOssService.generateUrl("bos:/version-cook/", fileId);
                
                // 保存图片信息到数据库
                saveRecipeImage(recipeId, fileId, imageUrl);
                
                log.info("菜谱图片上传成功，菜谱ID：{}，文件ID：{}", recipeId, fileId);
                return imageUrl;
                
            } finally {
                // 清理临时文件
                tempFile.delete();
            }
            
        } catch (Exception e) {
            log.error("菜谱图片上传失败", e);
            throw new BusinessException("图片上传失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取菜谱的所有图片URL
     */
    public List<String> getRecipeImageUrls(Long recipeId) {
        // 从数据库获取图片文件ID列表
        List<Long> fileIds = getRecipeImageFileIds(recipeId);
        
        if (fileIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 批量生成访问URL
        List<String> imageUrls = new ArrayList<>();
        for (Long fileId : fileIds) {
            try {
                String imageUrl = baiduOssService.generateUrl("bos:/version-cook/", fileId);
                imageUrls.add(imageUrl);
            } catch (Exception e) {
                log.warn("生成图片URL失败，文件ID：{}", fileId, e);
            }
        }
        
        return imageUrls;
    }
    
    private boolean isValidImageFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType != null && contentType.startsWith("image/");
    }
    
    private File convertToTempFile(MultipartFile multipartFile) throws IOException {
        String originalFilename = multipartFile.getOriginalFilename();
        String suffix = originalFilename != null && originalFilename.contains(".") 
            ? originalFilename.substring(originalFilename.lastIndexOf(".")) 
            : ".tmp";
        
        File tempFile = File.createTempFile("recipe_image_", suffix);
        multipartFile.transferTo(tempFile);
        return tempFile;
    }
    
    private void saveRecipeImage(Long recipeId, Long fileId, String imageUrl) {
        // 保存到数据库的逻辑
        // 例如：INSERT INTO recipe_images (recipe_id, file_id, image_url) VALUES (?, ?, ?)
    }
    
    private List<Long> getRecipeImageFileIds(Long recipeId) {
        // 从数据库查询的逻辑
        // 例如：SELECT file_id FROM recipe_images WHERE recipe_id = ?
        return Collections.emptyList(); // 示例返回
    }
}
```

### 2. 文件管理控制器扩展

```java
@RestController
@RequestMapping("/file-management")
public class FileManagementController {
    
    @Autowired
    private BaiduOssService baiduOssService;
    
    /**
     * 获取文件信息（包含URL）
     */
    @GetMapping("/info/{fileId}")
    public ResultData<FileInfo> getFileInfo(@PathVariable Long fileId) {
        try {
            // 从数据库获取文件基本信息
            FileInfo fileInfo = getFileInfoFromDatabase(fileId);
            
            if (fileInfo == null) {
                return ResultData.fail("文件不存在");
            }
            
            // 生成访问URL
            String fileUrl = baiduOssService.generateUrl("bos:/version-cook/", fileId);
            fileInfo.setFileUrl(fileUrl);
            
            return ResultData.success(fileInfo);
            
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return ResultData.fail("获取失败：" + e.getMessage());
        }
    }
    
    /**
     * 文件预览（重定向到实际URL）
     */
    @GetMapping("/preview/{fileId}")
    public void previewFile(@PathVariable Long fileId, HttpServletResponse response) {
        try {
            String fileUrl = baiduOssService.generateUrl("bos:/version-cook/", fileId);
            response.sendRedirect(fileUrl);
        } catch (Exception e) {
            log.error("文件预览失败", e);
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    private FileInfo getFileInfoFromDatabase(Long fileId) {
        // 从数据库查询文件信息的逻辑
        return null; // 示例返回
    }
    
    public static class FileInfo {
        private Long fileId;
        private String fileName;
        private String fileUrl;
        private Long fileSize;
        private String contentType;
        private Date uploadTime;
        
        // Getters and Setters
    }
}
```

## 移动端集成示例

### 1. Android (Kotlin)

```kotlin
class FileUploadManager(private val context: Context) {
    private val baseUrl = "http://your-server.com"
    private val token = "your_jwt_token"
    
    suspend fun uploadFile(file: File): UploadResult? {
        return withContext(Dispatchers.IO) {
            try {
                val requestBody = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                val multipartBody = MultipartBody.Part.createFormData("file", file.name, requestBody)
                
                val response = ApiClient.fileService.uploadFile(
                    authorization = "Bearer $token",
                    file = multipartBody
                )
                
                if (response.isSuccessful) {
                    response.body()?.data
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e("FileUpload", "上传失败", e)
                null
            }
        }
    }
    
    suspend fun getFileUrl(fileId: Long): String? {
        return withContext(Dispatchers.IO) {
            try {
                val response = ApiClient.fileService.getFileUrl(
                    authorization = "Bearer $token",
                    fileId = fileId
                )
                
                if (response.isSuccessful) {
                    response.body()?.data?.fileUrl
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e("FileUpload", "获取URL失败", e)
                null
            }
        }
    }
}
```

### 2. iOS (Swift)

```swift
class FileUploadManager {
    private let baseURL = "http://your-server.com"
    private let token = "your_jwt_token"
    
    func uploadFile(fileData: Data, fileName: String, completion: @escaping (UploadResult?) -> Void) {
        guard let url = URL(string: "\(baseURL)/baidu-oss/upload") else {
            completion(nil)
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        var body = Data()
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: application/octet-stream\r\n\r\n".data(using: .utf8)!)
        body.append(fileData)
        body.append("\r\n--\(boundary)--\r\n".data(using: .utf8)!)
        
        request.httpBody = body
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            guard let data = data, error == nil else {
                completion(nil)
                return
            }
            
            do {
                let result = try JSONDecoder().decode(ApiResponse<UploadResult>.self, from: data)
                completion(result.data)
            } catch {
                completion(nil)
            }
        }.resume()
    }
}
```

这些示例展示了如何在不同的前端和后端技术栈中集成百度OSS文件存储API，提供了完整的文件上传、URL获取和管理功能。
