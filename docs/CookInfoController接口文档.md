# CookInfoController 接口文档

## 概述
CookInfoController 是智能菜谱系统的核心控制器，提供食材图片上传、AI识别和历史记录查询功能。

**基础路径**: `/cook`

**作者**: jiajunwang  
**版权**: 铜豌豆-1forall.cn  
**创建时间**: 2025/6/14 11:35

---

## 接口列表

### 1. 上传食材图片（Base64格式）

**接口描述**: 支持Base64格式的多文件上传，用于食材识别

**请求信息**:
- **URL**: `POST /cook/upload/photos`
- **Content-Type**: `application/json`
- **Accept**: `application/json`

**请求参数**:
```json
{
  "files": [
    {
      "fileName": "image1.jpg",        // 文件名（必填）
      "fileId": 123456,                // 文件ID（可选）
      "fileSize": 1024000,             // 文件大小，单位字节（必填，必须大于0）
      "fileData": "data:image/jpeg;base64,/9j/4AAQ..."  // Base64编码的文件数据（必填）
    }
  ]
}
```

**参数说明**:
- `files`: 文件信息数组
  - `fileName`: 文件名，不能为空
  - `fileId`: 文件ID，可选字段
  - `fileSize`: 文件大小（字节），必须为正数
  - `fileData`: Base64编码的图片数据，不能为空

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "name": "西红柿",
      "rate": "0.95"
    },
    {
      "name": "鸡蛋",
      "rate": "0.88"
    }
  ]
}
```

---

### 2. 上传食材图片（MultipartFile格式）

**接口描述**: 支持MultipartFile格式的多文件上传，更适合前端直接上传文件

**请求信息**:
- **URL**: `POST /cook/upload/photos/multipart`
- **Content-Type**: `multipart/form-data`
- **Accept**: `application/json`

**请求参数**:
- `files`: 上传的文件数组（必填）
- `description`: 可选的描述信息（可选）
- `enableAiRecognition`: 是否启用AI识别，默认为true（可选）

**请求示例**（使用curl）:
```bash
curl -X POST "http://localhost:8080/cook/upload/photos/multipart" \
  -F "files=@image1.jpg" \
  -F "files=@image2.jpg" \
  -F "description=今天的食材" \
  -F "enableAiRecognition=true"
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "name": "土豆",
      "rate": "0.92"
    },
    {
      "name": "胡萝卜",
      "rate": "0.87"
    }
  ]
}
```

---

### 3. 分页查询历史记录

**接口描述**: 分页查询用户的食材识别历史记录

**请求信息**:
- **URL**: `POST /cook/version-records/page`
- **Content-Type**: `application/json`
- **Accept**: `application/json`

**请求参数**:
```json
{
  "userId": 123456,      // 用户ID（必填）
  "pageNum": 1,          // 页码，从1开始（可选，默认1）
  "pageSize": 10         // 每页大小（可选，默认10）
}
```

**参数说明**:
- `userId`: 用户ID，不能为空
- `pageNum`: 页码，必须大于0，默认为1
- `pageSize`: 每页大小，必须大于0，默认为10

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "list": [
      {
        "name": "西红柿",
        "rate": "0.95"
      },
      {
        "name": "鸡蛋",
        "rate": "0.88"
      }
    ],
    "total": 25,           // 总记录数
    "pageNum": 1,          // 当前页码
    "pageSize": 10,        // 每页大小
    "pages": 3,            // 总页数
    "hasNext": true,       // 是否有下一页
    "hasPrevious": false   // 是否有上一页
  }
}
```

---

## 数据模型

### CookAiVersionInfoVo（食材识别结果）
```json
{
  "name": "食材名称",
  "rate": "识别置信度（0-1之间的字符串）"
}
```

### ResultData（统一响应格式）
```json
{
  "code": 200,           // 状态码：200成功，其他失败
  "msg": "success",      // 响应消息
  "data": {}             // 响应数据
}
```

### PageResult（分页响应格式）
```json
{
  "list": [],            // 数据列表
  "total": 0,            // 总记录数
  "pageNum": 1,          // 当前页码
  "pageSize": 10,        // 每页大小
  "pages": 1,            // 总页数
  "hasNext": false,      // 是否有下一页
  "hasPrevious": false   // 是否有上一页
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

---

## 注意事项

1. **文件格式**: 支持常见的图片格式（jpg、jpeg、png等）
2. **文件大小**: 建议单个文件不超过5MB
3. **AI识别**: 使用百度AI进行食材识别，识别准确率受图片质量影响
4. **用户认证**: 所有接口都需要用户登录状态
5. **并发处理**: 支持多文件同时上传和识别
6. **错误处理**: 单个文件识别失败不影响其他文件的处理

---

## 使用示例

### JavaScript前端调用示例

#### 1. Base64格式上传
```javascript
const uploadBase64 = async (files) => {
  const response = await fetch('/cook/upload/photos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      files: files.map(file => ({
        fileName: file.name,
        fileSize: file.size,
        fileData: file.base64Data
      }))
    })
  });
  return response.json();
};
```

#### 2. MultipartFile格式上传
```javascript
const uploadMultipart = async (files, description = '', enableAi = true) => {
  const formData = new FormData();
  files.forEach(file => formData.append('files', file));
  formData.append('description', description);
  formData.append('enableAiRecognition', enableAi);
  
  const response = await fetch('/cook/upload/photos/multipart', {
    method: 'POST',
    body: formData
  });
  return response.json();
};
```

#### 3. 查询历史记录
```javascript
const getHistory = async (userId, pageNum = 1, pageSize = 10) => {
  const response = await fetch('/cook/version-records/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      userId,
      pageNum,
      pageSize
    })
  });
  return response.json();
};
```
