# 菜谱相关API文档

## 1. 搜索菜谱

### 接口地址
```
POST /recipe/search
```

### 功能说明
支持根据菜名模糊匹配或根据食材名称精确匹配查询菜谱列表

### 请求参数
```json
{
    "recipeName": "包菜炒",
    "ingredientNames": ["鸡蛋", "包菜", "粉丝"],
    "difficultyLevel": 3,
    "pageNum": 1,
    "pageSize": 10
}
```

### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| recipeName | String | 否 | 菜谱名称（模糊匹配） |
| ingredientNames | List<String> | 否 | 食材名称列表（精确匹配） |
| difficultyLevel | Integer | 否 | 难度等级（1-简单，2-中等，3-困难） |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |

### 响应数据
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "name": "包菜炒鸡蛋粉丝",
                "description": "包菜炒鸡蛋粉丝，是中国的一道日常生活中所熟知的菜品",
                "difficultyLevel": 3,
                "difficultyLevelDesc": "困难",
                "servings": 1
            }
        ],
        "total": 1,
        "pageNum": 1,
        "pageSize": 10,
        "pages": 1,
        "hasNext": false,
        "hasPrevious": false
    }
}
```

## 2. 根据食材快速搜索菜谱

### 接口地址
```
GET /recipe/search-by-ingredients?ingredientNames=鸡蛋,包菜&pageNum=1&pageSize=10
```

### 功能说明
根据食材名称快速搜索相关菜谱

### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| ingredientNames | String | 是 | 食材名称，多个用逗号分隔 |
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |

### 响应数据
同搜索菜谱接口

## 3. 获取菜谱详情

### 接口地址
```
GET /recipe/detail/{recipeId}
```

### 功能说明
根据菜谱ID获取菜谱详细信息，包含食材列表和制作步骤

### 路径参数
- recipeId: 菜谱ID

### 响应数据
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "包菜炒鸡蛋粉丝",
        "description": "包菜炒鸡蛋粉丝，是中国的一道日常生活中所熟知的菜品",
        "difficultyLevel": 3,
        "difficultyLevelDesc": "困难",
        "servings": 1,
        "ingredients": [
            {
                "ingredientId": 2,
                "ingredientName": "鸡蛋",
                "quantity": "2个",
                "type": "main",
                "typeDesc": "主料"
            },
            {
                "ingredientId": 1,
                "ingredientName": "包菜",
                "quantity": "半颗",
                "type": "main",
                "typeDesc": "主料"
            },
            {
                "ingredientId": 6,
                "ingredientName": "盐",
                "quantity": "适量",
                "type": "seasoning",
                "typeDesc": "调料"
            }
        ],
        "steps": [
            {
                "id": 1,
                "stepOrder": 1,
                "description": "胡萝卜、包菜切丝备用",
                "durationSeconds": null,
                "durationDesc": null
            },
            {
                "id": 2,
                "stepOrder": 2,
                "description": "粉丝冷水泡 1 小时，煮软捞出备用",
                "durationSeconds": null,
                "durationDesc": null
            },
            {
                "id": 3,
                "stepOrder": 3,
                "description": "鸡蛋打散，加入盐后搅拌 15 秒",
                "durationSeconds": 15,
                "durationDesc": "15秒"
            }
        ]
    }
}
```

## 4. 使用示例

### 4.1 根据菜名搜索
```bash
curl -X POST http://localhost:8080/recipe/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "recipeName": "炒鸡蛋",
    "pageNum": 1,
    "pageSize": 5
  }'
```

### 4.2 根据食材搜索
```bash
curl -X POST http://localhost:8080/recipe/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "ingredientNames": ["鸡蛋", "包菜"],
    "pageNum": 1,
    "pageSize": 10
  }'
```

### 4.3 快速食材搜索
```bash
curl -X GET "http://localhost:8080/recipe/search-by-ingredients?ingredientNames=鸡蛋,包菜&pageNum=1&pageSize=10" \
  -H "Authorization: Bearer your_jwt_token"
```

### 4.4 获取菜谱详情
```bash
curl -X GET http://localhost:8080/recipe/detail/1 \
  -H "Authorization: Bearer your_jwt_token"
```

## 5. 前端集成示例

### 5.1 搜索菜谱
```javascript
function searchRecipes(searchParams) {
  const token = wx.getStorageSync('jwt_token');
  
  wx.request({
    url: 'https://your-domain.com/recipe/search',
    method: 'POST',
    header: {
      'content-type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    data: searchParams,
    success: function(res) {
      if (res.data.code === 200) {
        const recipes = res.data.data.list;
        // 处理菜谱列表
        console.log('搜索到的菜谱：', recipes);
      }
    }
  });
}

// 使用示例
searchRecipes({
  recipeName: "炒鸡蛋",
  pageNum: 1,
  pageSize: 10
});
```

### 5.2 根据识别的食材搜索菜谱
```javascript
function searchRecipesByIngredients(ingredients) {
  const ingredientNames = ingredients.map(item => item.name);
  
  searchRecipes({
    ingredientNames: ingredientNames,
    pageNum: 1,
    pageSize: 10
  });
}

// 结合食材识别功能
function onIngredientsRecognized(recognizedIngredients) {
  // recognizedIngredients 是从菜谱识别接口返回的食材列表
  searchRecipesByIngredients(recognizedIngredients);
}
```

### 5.3 获取菜谱详情
```javascript
function getRecipeDetail(recipeId) {
  const token = wx.getStorageSync('jwt_token');
  
  wx.request({
    url: `https://your-domain.com/recipe/detail/${recipeId}`,
    method: 'GET',
    header: {
      'Authorization': 'Bearer ' + token
    },
    success: function(res) {
      if (res.data.code === 200) {
        const recipe = res.data.data;
        // 处理菜谱详情
        console.log('菜谱详情：', recipe);
        displayRecipeDetail(recipe);
      }
    }
  });
}

function displayRecipeDetail(recipe) {
  // 显示菜谱基本信息
  console.log('菜名：', recipe.name);
  console.log('难度：', recipe.difficultyLevelDesc);
  
  // 显示食材列表
  recipe.ingredients.forEach(ingredient => {
    console.log(`${ingredient.typeDesc}：${ingredient.ingredientName} ${ingredient.quantity}`);
  });
  
  // 显示制作步骤
  recipe.steps.forEach(step => {
    console.log(`步骤${step.stepOrder}：${step.description}`);
    if (step.durationDesc) {
      console.log(`用时：${step.durationDesc}`);
    }
  });
}
```

## 6. 注意事项

1. **权限验证**: 所有菜谱接口都需要JWT token验证
2. **分页处理**: 搜索接口支持分页，建议合理设置pageSize
3. **食材匹配**: 食材搜索是精确匹配，需要确保食材名称准确
4. **性能优化**: 大量数据时建议使用分页加载
5. **错误处理**: 建议在前端添加完善的错误处理逻辑
