# 用户信息相关API文档

## 1. 微信小程序登录

### 接口地址
```
POST /user/wx-login
```

### 请求参数
```json
{
    "code": "微信小程序登录凭证",
    "userInfo": {
        "nickName": "用户昵称",
        "avatarUrl": "用户头像URL",
        "gender": 1,
        "country": "中国",
        "province": "广东省",
        "city": "深圳市",
        "language": "zh_CN"
    }
}
```

### 响应数据
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "userId": 123,
        "token": "cook_token_123_1640995200000",
        "expireTime": 1641600000000,
        "userInfo": {
            "id": 123,
            "nickname": "用户昵称",
            "avatarUrl": "用户头像URL",
            "gender": 1,
            "country": "中国",
            "province": "广东省",
            "city": "深圳市",
            "language": "zh_CN",
            "phone": null,
            "status": 0,
            "lastLoginTime": "2025-06-25T10:00:00",
            "createTime": "2025-06-25T10:00:00"
        }
    }
}
```

## 2. 获取用户信息

### 接口地址
```
GET /user/info/{userId}
```

### 路径参数
- userId: 用户ID

### 响应数据
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "id": 123,
        "nickname": "用户昵称",
        "avatarUrl": "用户头像URL",
        "gender": 1,
        "country": "中国",
        "province": "广东省",
        "city": "深圳市",
        "language": "zh_CN",
        "phone": null,
        "status": 0,
        "lastLoginTime": "2025-06-25T10:00:00",
        "createTime": "2025-06-25T10:00:00"
    }
}
```

## 3. 刷新用户token

### 接口地址
```
POST /user/refresh-token/{userId}
```

### 路径参数
- userId: 用户ID

### 响应数据
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "userId": 123,
        "token": "cook_token_123_1640995200000",
        "expireTime": 1641600000000,
        "userInfo": {
            "id": 123,
            "nickname": "用户昵称",
            "avatarUrl": "用户头像URL",
            "gender": 1,
            "country": "中国",
            "province": "广东省",
            "city": "深圳市",
            "language": "zh_CN",
            "phone": null,
            "status": 0,
            "lastLoginTime": "2025-06-25T10:00:00",
            "createTime": "2025-06-25T10:00:00"
        }
    }
}
```

## 微信小程序前端集成示例

### 1. 获取登录凭证
```javascript
// 在小程序中获取code
wx.login({
  success: function(res) {
    if (res.code) {
      // 调用后端登录接口
      wxLogin(res.code);
    }
  }
});
```

### 2. 调用登录接口
```javascript
function wxLogin(code) {
  // 获取用户信息
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: function(userRes) {
      // 调用后端登录接口
      wx.request({
        url: 'https://your-domain.com/user/wx-login',
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          code: code,
          userInfo: {
            nickName: userRes.userInfo.nickName,
            avatarUrl: userRes.userInfo.avatarUrl,
            gender: userRes.userInfo.gender,
            country: userRes.userInfo.country,
            province: userRes.userInfo.province,
            city: userRes.userInfo.city,
            language: userRes.userInfo.language
          }
        },
        success: function(res) {
          if (res.data.code === 200) {
            // 登录成功，保存token和用户信息
            wx.setStorageSync('token', res.data.data.token);
            wx.setStorageSync('userInfo', res.data.data.userInfo);
          }
        }
      });
    }
  });
}
```

## 配置说明

### 1. 微信小程序配置
在 `application.yml` 中配置微信小程序的appid和secret：

```yaml
wx:
  miniapp:
    appid: your_wx_miniapp_appid
    secret: your_wx_miniapp_secret
```

### 2. 数据库配置
执行 `src/main/resources/sql/user_info.sql` 中的SQL语句创建用户表。

## JWT Token使用说明

### 1. Token格式
系统使用JWT（JSON Web Token）作为身份验证机制，token格式为：
```
Bearer eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOjEyMywib3BlbmlkIjoidGVzdF9vcGVuaWQiLCJuaWNrbmFtZSI6IuWwj+eoi+W6j+eUqOaItyIsInN1YiI6IjEyMyIsImlzcyI6ImNvb2stYXBwIiwiaWF0IjoxNjQwOTk1MjAwLCJleHAiOjE2NDE2MDAwMDB9.signature
```

### 2. 请求头设置
在需要身份验证的接口中，需要在请求头中添加：
```
Authorization: Bearer <your_jwt_token>
```

### 3. Token包含信息
JWT token中包含以下用户信息：
- userId: 用户ID
- openid: 微信openid
- nickname: 用户昵称
- iss: 签发者
- iat: 签发时间
- exp: 过期时间

### 4. 受保护的接口
以下接口需要JWT token验证：
- `/cook/**` - 所有菜谱相关接口
- `/user/info/**` - 获取用户信息接口

### 5. 不需要验证的接口
- `/user/wx-login` - 微信登录
- `/user/refresh-token/**` - 刷新token

## 前端集成示例（更新版）

### 1. 登录并保存token
```javascript
function wxLogin(code) {
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: function(userRes) {
      wx.request({
        url: 'https://your-domain.com/user/wx-login',
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          code: code,
          userInfo: {
            nickName: userRes.userInfo.nickName,
            avatarUrl: userRes.userInfo.avatarUrl,
            gender: userRes.userInfo.gender,
            country: userRes.userInfo.country,
            province: userRes.userInfo.province,
            city: userRes.userInfo.city,
            language: userRes.userInfo.language
          }
        },
        success: function(res) {
          if (res.data.code === 200) {
            // 保存JWT token
            wx.setStorageSync('jwt_token', res.data.data.token);
            wx.setStorageSync('userInfo', res.data.data.userInfo);
          }
        }
      });
    }
  });
}
```

### 2. 在请求中使用token
```javascript
function makeAuthenticatedRequest(url, data) {
  const token = wx.getStorageSync('jwt_token');

  wx.request({
    url: url,
    method: 'POST',
    header: {
      'content-type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    data: data,
    success: function(res) {
      if (res.statusCode === 401) {
        // token过期或无效，重新登录
        redirectToLogin();
      } else {
        // 处理正常响应
        console.log(res.data);
      }
    },
    fail: function(err) {
      console.error('请求失败', err);
    }
  });
}
```

### 3. Token过期处理
```javascript
function handleTokenExpired() {
  // 清除本地存储的token
  wx.removeStorageSync('jwt_token');
  wx.removeStorageSync('userInfo');

  // 重新登录
  wx.login({
    success: function(res) {
      if (res.code) {
        wxLogin(res.code);
      }
    }
  });
}
```

## 注意事项

1. **安全性**: 系统已使用JWT作为身份验证机制，提供了更好的安全性
2. **Token过期**: JWT token默认7天过期，过期后需要重新登录
3. **密钥安全**: 生产环境请修改JWT密钥配置
4. **微信配置**: 需要在微信公众平台配置正确的appid和secret
5. **用户信息**: 微信小程序获取用户信息需要用户授权
6. **错误处理**: 建议在前端添加完善的错误处理逻辑，特别是token过期的处理
