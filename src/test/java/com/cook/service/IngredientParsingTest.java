//package com.cook.service;
//
//import com.cook.service.impl.MarkdownRecipeParser;
//import com.cook.service.impl.MarkdownRecipeParser.ParsedRecipe;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * <AUTHOR>
// * @description 食材解析测试
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class IngredientParsingTest {
//
//    @Autowired
//    private MarkdownRecipeParser markdownRecipeParser;
//
//    @Test
//    public void testStandardizeIngredientNames() {
//        String markdownContent = """
//                # 测试菜谱的做法
//
//                ## 计算
//
//                - 干辣椒 5g
//                - 干辣椒（或者二荆条） 10g
//                - 大蒜 1 个（约 20g）
//                - 新鲜菜心 250g
//                - 生抽、蚝油、盐 适量
//                - 老姜 一块
//                - 小葱 2根
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("食材标准化解析结果：");
//        for (var ingredient : recipe.getIngredients()) {
//            log.info("食材：{} - 数量：{} - 类型：{}",
//                    ingredient.getName(),
//                    ingredient.getQuantity(),
//                    ingredient.getType());
//        }
//
//        // 验证标准化效果
//        boolean hasDryChili = recipe.getIngredients().stream()
//                .anyMatch(i -> "辣椒".equals(i.getName()));
//
//        boolean hasGarlic = recipe.getIngredients().stream()
//                .anyMatch(i -> "蒜".equals(i.getName()));
//
//        boolean hasVegetableHeart = recipe.getIngredients().stream()
//                .anyMatch(i -> "菜心".equals(i.getName()));
//
//        boolean hasSoySauce = recipe.getIngredients().stream()
//                .anyMatch(i -> "生抽".equals(i.getName()));
//
//        boolean hasOysterSauce = recipe.getIngredients().stream()
//                .anyMatch(i -> "蚝油".equals(i.getName()));
//
//        boolean hasSalt = recipe.getIngredients().stream()
//                .anyMatch(i -> "盐".equals(i.getName()));
//
//        boolean hasGinger = recipe.getIngredients().stream()
//                .anyMatch(i -> "姜".equals(i.getName()));
//
//        boolean hasScallion = recipe.getIngredients().stream()
//                .anyMatch(i -> "葱".equals(i.getName()));
//
//        assert hasDryChili : "应该包含标准化的辣椒";
//        assert hasGarlic : "应该包含标准化的蒜";
//        assert hasVegetableHeart : "应该包含标准化的菜心";
//        assert hasSoySauce : "应该包含生抽";
//        assert hasOysterSauce : "应该包含蚝油";
//        assert hasSalt : "应该包含盐";
//        assert hasGinger : "应该包含标准化的姜";
//        assert hasScallion : "应该包含标准化的葱";
//
//        log.info("✅ 食材标准化解析测试通过");
//    }
//
//    @Test
//    public void testComplexIngredientNames() {
//        String markdownContent = """
//                # 复杂食材测试的做法
//
//                ## 必备原料和工具
//
//                - 干辣椒（或者二荆条、小米椒）
//                - 大蒜 1 个（约 20g，去皮）
//                - 新鲜菜心/小白菜 250g
//                - 生抽、老抽、蚝油
//                - 熟猪油或菜籽油
//                - 老姜一块，切片
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("复杂食材解析结果：");
//        for (var ingredient : recipe.getIngredients()) {
//            log.info("食材：{} - 数量：{} - 类型：{}",
//                    ingredient.getName(),
//                    ingredient.getQuantity(),
//                    ingredient.getType());
//        }
//
//        // 验证复杂情况的处理
//        long ingredientCount = recipe.getIngredients().size();
//        assert ingredientCount >= 8 : "应该解析出至少8种食材";
//
//        // 验证特殊字符处理
//        boolean hasCleanNames = recipe.getIngredients().stream()
//                .allMatch(i -> !i.getName().contains("（") &&
//                              !i.getName().contains("(") &&
//                              !i.getName().contains("，") &&
//                              !i.getName().contains("或"));
//
//        assert hasCleanNames : "所有食材名称应该不包含特殊字符";
//
//        log.info("✅ 复杂食材解析测试通过");
//    }
//
//    @Test
//    public void testIngredientTypeClassification() {
//        String markdownContent = """
//                # 食材分类测试的做法
//
//                ## 计算
//
//                - 猪肉 500g
//                - 大白菜 300g
//                - 生抽 15ml
//                - 蚝油 10ml
//                - 盐 5g
//                - 糖 3g
//                - 料酒 20ml
//                - 鸡汤 200ml
//                - 清水 100ml
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("食材分类测试结果：");
//        for (var ingredient : recipe.getIngredients()) {
//            log.info("食材：{} - 类型：{}", ingredient.getName(), ingredient.getType());
//        }
//
//        // 验证分类正确性
//        long mainCount = recipe.getIngredients().stream()
//                .filter(i -> "main".equals(i.getType()))
//                .count();
//
//        long seasoningCount = recipe.getIngredients().stream()
//                .filter(i -> "seasoning".equals(i.getType()))
//                .count();
//
//        long supplementCount = recipe.getIngredients().stream()
//                .filter(i -> "supplement".equals(i.getType()))
//                .count();
//
//        assert mainCount >= 2 : "应该有至少2种主料";
//        assert seasoningCount >= 4 : "应该有至少4种调料";
//        assert supplementCount >= 2 : "应该有至少2种辅料";
//
//        log.info("分类统计 - 主料：{}，调料：{}，辅料：{}", mainCount, seasoningCount, supplementCount);
//        log.info("✅ 食材分类测试通过");
//    }
//}
