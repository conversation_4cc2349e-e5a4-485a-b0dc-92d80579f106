package com.cook.service;

import cn.hutool.json.JSONUtil;
import com.cook.model.base.ResultData;
import com.cook.model.dto.CookVersionRecordPageDto;
import com.cook.model.vo.CookAiVersionInfoVo;
import com.cook.model.vo.PageResult;
import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @description CookInfoService测试类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
//@SpringBootTest
public class CookInfoServiceTest {

//    @Autowired
//    private CookInfoService cookInfoService;
//
//    @Test
//    public void testGetVersionRecordsPage() {
//        // 创建分页查询参数
//        CookVersionRecordPageDto dto = new CookVersionRecordPageDto();
//        dto.setUserId(0L); // 使用测试用户ID
//        dto.setPageNum(1);
//        dto.setPageSize(10);
//
//        // 执行分页查询
//        ResultData<PageResult<CookAiVersionInfoVo>> result = cookInfoService.getVersionRecordsPage(dto);
//
//        // 输出结果
//        log.info("分页查询结果：{}", JSONUtil.toJsonStr(result));
//
//        // 验证结果
//        assert result != null;
//        assert result.getCode() == 200; // 假设成功码是200
//
//        if (result.getData() != null) {
//            PageResult<CookAiVersionInfoVo> pageResult = result.getData();
//            log.info("总记录数：{}", pageResult.getTotal());
//            log.info("当前页码：{}", pageResult.getPageNum());
//            log.info("每页大小：{}", pageResult.getPageSize());
//            log.info("总页数：{}", pageResult.getPages());
//            log.info("数据列表大小：{}", pageResult.getList() != null ? pageResult.getList().size() : 0);
//
//            if (pageResult.getList() != null && !pageResult.getList().isEmpty()) {
//                log.info("第一条数据：{}", JSONUtil.toJsonStr(pageResult.getList().get(0)));
//            }
//        }
//    }
}
