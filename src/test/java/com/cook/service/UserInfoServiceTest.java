//package com.cook.service;
//
//import cn.hutool.json.JSONUtil;
//import com.cook.model.base.ResultData;
//import com.cook.model.dto.WxLoginDto;
//import com.cook.model.vo.LoginResultVo;
//import com.cook.model.vo.UserInfoVo;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * <AUTHOR>
// * @description 用户信息服务测试类
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class UserInfoServiceTest {
//
//    @Autowired
//    private UserInfoService userInfoService;
//
//    @Test
//    public void testWxLogin() {
//        // 创建登录参数（注意：这里的code需要从微信小程序前端获取）
//        WxLoginDto dto = new WxLoginDto();
//        dto.setCode("test_code_from_wx_miniapp");
//
//        // 设置用户信息
//        WxLoginDto.UserInfoDto userInfo = new WxLoginDto.UserInfoDto();
//        userInfo.setNickName("测试用户");
//        userInfo.setAvatarUrl("https://example.com/avatar.jpg");
//        userInfo.setGender(1);
//        userInfo.setCountry("中国");
//        userInfo.setProvince("广东省");
//        userInfo.setCity("深圳市");
//        userInfo.setLanguage("zh_CN");
//        dto.setUserInfo(userInfo);
//
//        // 执行登录（注意：这个测试需要配置正确的微信小程序appid和secret才能成功）
//        ResultData<LoginResultVo> result = userInfoService.wxLogin(dto);
//
//        // 输出结果
//        log.info("微信登录结果：{}", JSONUtil.toJsonStr(result));
//    }
//
//    @Test
//    public void testGetUserInfo() {
//        // 测试获取用户信息（需要先有用户数据）
//        Long userId = 1L;
//        ResultData<UserInfoVo> result = userInfoService.getUserInfo(userId);
//
//        log.info("获取用户信息结果：{}", JSONUtil.toJsonStr(result));
//    }
//
//    @Test
//    public void testRefreshToken() {
//        // 测试刷新token（需要先有用户数据）
//        Long userId = 1L;
//        ResultData<LoginResultVo> result = userInfoService.refreshToken(userId);
//
//        log.info("刷新token结果：{}", JSONUtil.toJsonStr(result));
//    }
//}
