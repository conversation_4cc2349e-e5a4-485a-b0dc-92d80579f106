//package com.cook.service;
//
//import cn.hutool.json.JSONUtil;
//import com.cook.model.dto.BaiDuTokenResult;
//import com.cook.model.dto.BaiDuVersionsResult;
//import com.cook.service.client.BaiduServiceClient;
//import com.cook.service.impl.BaiduIngredientService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * <AUTHOR>
// * @description 百度Feign客户端测试
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class BaiduFeignClientTest {
//
//    @Autowired
//    private BaiduServiceClient baiduServiceClient;
//
//    @Autowired
//    private BaiduIngredientService baiduIngredientService;
//
//    @Value("${baidu.grant.type:client_credentials}")
//    private String baiduGrantType;
//    @Value("${baidu.api.key:}")
//    private String baiduApiKey;
//    @Value("${baidu.api.secret:}")
//    private String baiduApiSecret;
//
//    @Test
//    public void testGetToken() {
//        try {
//            BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
//            log.info("Token获取结果：{}", JSONUtil.toJsonStr(token));
//
//            if (token != null && token.getAccessToken() != null) {
//                log.info("Token获取成功");
//            } else {
//                log.error("Token获取失败");
//            }
//        } catch (Exception e) {
//            log.error("Token获取异常", e);
//        }
//    }
//
//    @Test
//    public void testClassifyIngredient() {
//        try {
//            // 先获取token
//            BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
//            if (token == null || token.getAccessToken() == null) {
//                log.error("无法获取token，跳过测试");
//                return;
//            }
//
//            // 使用一个很小的测试图片base64
//            String testImage = "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";
//
//            // 测试新的服务
//            BaiDuVersionsResult result = baiduIngredientService.classifyIngredient(token.getAccessToken(), testImage);
//            log.info("食材识别结果：{}", JSONUtil.toJsonStr(result));
//
//        } catch (Exception e) {
//            log.error("食材识别测试异常", e);
//            // 打印更详细的异常信息
//            if (e.getCause() != null) {
//                log.error("异常原因：{}", e.getCause().getMessage());
//            }
//        }
//    }
//}
