//package com.cook.service;
//
//import com.cook.service.MarkdownRecipeParser.ParsedRecipe;
//import lombok.extern.slf4j.Slf4j;
////import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * <AUTHOR>
// * @description 步骤解析测试
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class StepParsingTest {
//
//    @Autowired
//    private MarkdownRecipeParser markdownRecipeParser;
//
//    @Test
//    public void testNumberedSteps() {
//        String markdownContent = """
//                # 白灼菜心的做法
//
//                ## 操作
//
//                1. 菜心洗净，去除根部比较硬或老的地方
//                2. 大蒜切成蒜末，有洋葱顺便加了点洋葱
//                3. 调制灵魂料汁：生抽5g、蚝油5g，加3g糖和100g清水半碗成一碗汁儿
//                4. 一锅500ml清水加5g盐和10g食用油烧开
//                5. 将菜心根茎在沸水中烫1分钟，直到根茎颜色变成深绿
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("有序号步骤解析结果：");
//        for (int i = 0; i < recipe.getSteps().size(); i++) {
//            log.info("步骤 {}: {}", i + 1, recipe.getSteps().get(i).getDescription());
//        }
//
//        assert recipe.getSteps().size() == 5;
//        assert recipe.getSteps().get(0).getDescription().contains("菜心洗净");
//        assert recipe.getSteps().get(4).getDescription().contains("烫1分钟");
//
//        log.info("✅ 有序号步骤解析测试通过");
//    }
//
//    @Test
//    public void testBulletPointSteps() {
//        String markdownContent = """
//                # 红烧肉的做法
//
//                ## 操作
//
//                - 将肉刮洗干净，入煮锅煮至六成熟（变色为白），捞出趁热用蜂蜜、醋涂抹肉皮。
//                - 炒锅内放入熟猪油，用旺火烧至八成熟（约 200 度，油表有大量青烟，油状平静），将肉块皮朝下投入，炸至呈金红色时，捞入凉肉煮锅（之前煮完的煮锅）中泡软，放在案板上，切成三寸(10 cm)长、两分(0.6 cm)厚的片，仍然皮朝下，整齐装入蒸碗内。
//                - 将 5 克大葱切成 2.4 cm 长的段，5 克切成 2.4 cm 长的斜形片。姜去皮洗净，1.5 克切成片，5 克切成末，摊的鸡蛋皮切成 2.4 cm 长的等腰三角形片。
//                - 商芝入沸水锅中煮软捞出，去除老茎、杂质，淘洗干净，切成 3 cm 长的段，放入碗中,加酱油（5 克）、精盐（1 克）、熟猪油（10 克）拌匀，盖在肉片上，另将鸡汤（100 克）放入一小碗中，加酱油（5 克）、精盐（0.5 克）、料酒（15 克）搅匀，浇入蒸碗，再放入姜片、葱段、八角上笼用旺火蒸约半小时后，转用小火继续蒸约一小时三十分钟，熟烂后取出，拣去姜、葱、八角，倒、过滤原汁，将肉扣入汤盘。
//                - 炒锅内，放入鸡汤（100 克），加入原汁，用旺火烧沸，下入姜末、葱片、味精后搅匀，投入摊鸡蛋皮，淋芝麻油，浇入汤盘即成。
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("无序号步骤解析结果：");
//        for (int i = 0; i < recipe.getSteps().size(); i++) {
//            log.info("步骤 {}: {}", i + 1, recipe.getSteps().get(i).getDescription());
//        }
//
//        assert recipe.getSteps().size() == 5;
//        assert recipe.getSteps().get(0).getDescription().contains("将肉刮洗干净");
//        assert recipe.getSteps().get(1).getDescription().contains("炒锅内放入熟猪油");
//        assert recipe.getSteps().get(4).getDescription().contains("炒锅内，放入鸡汤");
//
//        log.info("✅ 无序号步骤解析测试通过");
//    }
//
//    @Test
//    public void testMixedContent() {
//        String markdownContent = """
//                # 测试菜谱的做法
//
//                ## 操作
//
//                这里有一些说明文字。
//
//                - 第一步：准备食材
//                - 第二步：开始烹饪
//                  继续第二步的详细说明
//                - 第三步：完成制作
//
//                注意事项：记得关火。
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("混合内容步骤解析结果：");
//        for (int i = 0; i < recipe.getSteps().size(); i++) {
//            log.info("步骤 {}: {}", i + 1, recipe.getSteps().get(i).getDescription());
//        }
//
//        assert recipe.getSteps().size() == 3;
//        assert recipe.getSteps().get(0).getDescription().contains("第一步");
//        assert recipe.getSteps().get(1).getDescription().contains("第二步");
//        assert recipe.getSteps().get(2).getDescription().contains("第三步");
//
//        log.info("✅ 混合内容步骤解析测试通过");
//    }
//
//    @Test
//    public void testEmptySteps() {
//        String markdownContent = """
//                # 简单菜谱的做法
//
//                ## 操作
//
//                没有具体步骤，只有说明文字。
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("空步骤解析结果：步骤数量 = {}", recipe.getSteps().size());
//
//        // 应该返回空列表或者没有步骤
//        assert recipe.getSteps().size() == 0;
//
//        log.info("✅ 空步骤解析测试通过");
//    }
//}
