//package com.cook.service;
//
//import com.cook.service.MarkdownRecipeParser.ParsedRecipe;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * <AUTHOR>
// * @description Markdown描述提取测试
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class MarkdownDescriptionTest {
//
//    @Autowired
//    private MarkdownRecipeParser markdownRecipeParser;
//
//    @Test
//    public void testExtractDescriptionWithComments() {
//        String markdownContent = """
//                # 白灼菜心的做法
//
//                <!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->
//
//                ![白灼菜心](./白灼菜心.jpg)
//
//                > 没有拍照，上图是网图，不过做出来都差不多啦
//
//                白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。
//
//                总之吧，减肥或者是**快速解决绿叶菜的绝佳方式**。
//
//                预估烹饪难度：★★
//
//                ## 必备原料和工具
//
//                - 新鲜菜心
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("提取的描述：{}", recipe.getDescription());
//
//        // 验证描述不包含注释、图片链接和引用块
//        String description = recipe.getDescription();
//
//        // 不应该包含HTML注释
//        assert !description.contains("标题必须是");
//        assert !description.contains("<!--");
//        assert !description.contains("-->");
//
//        // 不应该包含图片链接
//        assert !description.contains("![白灼菜心]");
//        assert !description.contains("./白灼菜心.jpg");
//
//        // 不应该包含引用块内容
//        assert !description.contains("没有拍照");
//        assert !description.contains("上图是网图");
//
//        // 应该包含实际的描述内容
//        assert description.contains("白灼菜心是经典粤菜");
//        assert description.contains("快速解决绿叶菜的绝佳方式");
//
//        // 验证Markdown格式被清理
//        assert !description.contains("**");
//
//        log.info("✅ 描述提取测试通过");
//        log.info("清理后的描述：{}", description);
//    }
//
//    @Test
//    public void testExtractDescriptionWithComplexMarkdown() {
//        String markdownContent = """
//                # 复杂菜谱的做法
//
//                <!-- 这是一个注释 -->
//                <!-- 多行注释
//                     内容 -->
//
//                ![菜谱图片](./image.jpg) 这里有图片
//
//                > 这是一个引用块
//                > 包含多行内容
//
//                这是**粗体文字**和*斜体文字*以及`代码文字`。
//
//                这里有[链接文字](http://example.com)和行内图片![小图](./small.jpg)内容。
//
//                这是正常的描述文字，应该被保留。
//
//                ## 下一个章节
//
//                这里不应该被包含在描述中。
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("复杂Markdown提取的描述：{}", recipe.getDescription());
//
//        String description = recipe.getDescription();
//
//        // 验证清理效果
//        assert !description.contains("<!--");
//        assert !description.contains("![");
//        assert !description.contains(">");
//        assert !description.contains("**");
//        assert !description.contains("*");
//        assert !description.contains("`");
//        assert !description.contains("[");
//        assert !description.contains("](");
//
//        // 验证保留的内容
//        assert description.contains("粗体文字");
//        assert description.contains("斜体文字");
//        assert description.contains("代码文字");
//        assert description.contains("链接文字");
//        assert description.contains("正常的描述文字");
//
//        // 验证不包含后续章节内容
//        assert !description.contains("下一个章节");
//        assert !description.contains("这里不应该被包含");
//
//        log.info("✅ 复杂Markdown描述提取测试通过");
//    }
//
//    @Test
//    public void testExtractDescriptionEmpty() {
//        String markdownContent = """
//                # 只有标题的做法
//
//                <!-- 只有注释 -->
//
//                ![只有图片](./image.jpg)
//
//                > 只有引用
//
//                ## 直接到下一章节
//                """;
//
//        ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
//
//        log.info("空描述测试结果：'{}'", recipe.getDescription());
//
//        // 应该返回空字符串或默认描述
//        String description = recipe.getDescription();
//        assert description == null || description.trim().isEmpty();
//
//        log.info("✅ 空描述测试通过");
//    }
//}
