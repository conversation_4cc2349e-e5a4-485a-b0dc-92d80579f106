package com.cook.service;

import com.cook.model.dto.WxSessionResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @description 微信API服务测试
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/7/12
 */
@Slf4j
@SpringBootTest
public class WxApiServiceTest {

    @Autowired
    private WxApiService wxApiService;

    @Test
    public void testCode2Session() {
        // 注意：这个测试需要真实的微信小程序code才能成功
        // 这里只是测试服务是否能正常调用，不会成功获取到真实数据
        
        String appid = "wxbe4f9d4bae7148d2";
        String secret = "efb3ff0e65fa5224536aa0d1341fa2be";
        String testCode = "test_code";
        String grantType = "authorization_code";
        
        try {
            WxSessionResult result = wxApiService.code2Session(appid, secret, testCode, grantType);
            log.info("微信API调用结果: {}", result);
            
            // 验证结果不为null
            assert result != null;
            
            // 如果有错误码，应该是40029（code无效）或其他微信错误，而不是解析错误
            if (result.getErrcode() != null) {
                log.info("微信API返回错误码: {}, 错误信息: {}", result.getErrcode(), result.getErrmsg());
                // 40029 = invalid code, 这是预期的，因为我们使用的是测试code
                assert result.getErrcode() == 40029 || result.getErrcode() == 40013;
            }
            
        } catch (Exception e) {
            log.error("微信API调用异常", e);
            // 如果是解析异常，说明我们的修复没有生效
            if (e.getMessage().contains("HttpMessageConverter")) {
                throw new AssertionError("RestTemplate仍然无法解析微信API响应", e);
            }
        }
    }
}
