//package com.cook.service;
//
//import cn.hutool.json.JSONUtil;
//import com.cook.model.dto.BaiDuTokenResult;
//import com.cook.model.dto.BaiDuVersionsResult;
//import com.cook.service.client.BaiduServiceClient;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * <AUTHOR>
// * @description 百度服务客户端测试类
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class BaiduServiceClientTest {
//
//    @Autowired
//    private BaiduServiceClient baiduServiceClient;
//
//    @Value("${baidu.grant.type:client_credentials}")
//    private String baiduGrantType;
//    @Value("${baidu.api.key:}")
//    private String baiduApiKey;
//    @Value("${baidu.api.secret:}")
//    private String baiduApiSecret;
//
//    @Test
//    public void testGetAccessToken() {
//        // 测试获取访问令牌
//        BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
//        log.info("获取到的token：{}", JSONUtil.toJsonStr(token));
//
//        assert token != null;
//        assert token.getAccessToken() != null;
//        log.info("Token获取成功，access_token: {}", token.getAccessToken());
//    }
//
//    @Test
//    public void testClassifyIngredient() {
//        // 首先获取token
//        BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
//        assert token != null && token.getAccessToken() != null;
//
//        // 测试用的base64图片数据（这里需要替换为实际的base64数据）
//        String testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
//
//        try {
//            // 测试基础食材识别接口
//            BaiDuVersionsResult result = baiduServiceClient.classifyIngredient(
//                    token.getAccessToken(),
//                    testImageBase64
//            );
//
//            log.info("基础识别结果：{}", JSONUtil.toJsonStr(result));
//
//        } catch (Exception e) {
//            log.error("基础识别接口调用失败", e);
//        }
//    }
//
//    @Test
//    public void testClassifyIngredientWithTopNum() {
//        // 首先获取token
//        BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
//        assert token != null && token.getAccessToken() != null;
//
//        // 测试用的base64图片数据
//        String testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
//
//        try {
//            // 测试带top_num参数的食材识别接口
//            BaiDuVersionsResult result = baiduServiceClient.classifyIngredientWithTopNum(
//                    token.getAccessToken(),
//                    testImageBase64,
//                    5
//            );
//
//            log.info("带top_num参数的识别结果：{}", JSONUtil.toJsonStr(result));
//
//        } catch (Exception e) {
//            log.error("带top_num参数的识别接口调用失败", e);
//        }
//    }
//
//    @Test
//    public void testClassifyIngredientByUrl() {
//        // 首先获取token
//        BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
//        assert token != null && token.getAccessToken() != null;
//
//        // 测试用的图片URL
//        String testImageUrl = "https://example.com/test-image.jpg";
//
//        try {
//            // 测试URL方式的食材识别接口
//            BaiDuVersionsResult result = baiduServiceClient.classifyIngredientByUrl(
//                    token.getAccessToken(),
//                    testImageUrl
//            );
//
//            log.info("URL方式识别结果：{}", JSONUtil.toJsonStr(result));
//
//        } catch (Exception e) {
//            log.error("URL方式识别接口调用失败", e);
//        }
//    }
//}
