//package com.cook.service;
//
//import cn.hutool.json.JSONUtil;
//import com.cook.model.base.ResultData;
//import com.cook.model.dto.RecipeSearchDto;
//import com.cook.model.vo.PageResult;
//import com.cook.model.vo.RecipeDetailVo;
//import com.cook.model.vo.RecipeListVo;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.Arrays;
//
///**
// * <AUTHOR>
// * @description 菜谱服务测试类
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class RecipeServiceTest {
//
//    @Autowired
//    private RecipeService recipeService;
//
//    @Test
//    public void testSearchRecipesByName() {
//        // 测试根据菜名搜索
//        RecipeSearchDto dto = new RecipeSearchDto();
//        dto.setRecipeName("包菜");
//        dto.setPageNum(1);
//        dto.setPageSize(10);
//
//        ResultData<PageResult<RecipeListVo>> result = recipeService.searchRecipes(dto);
//        log.info("根据菜名搜索结果：{}", JSONUtil.toJsonStr(result));
//    }
//
//    @Test
//    public void testSearchRecipesByIngredients() {
//        // 测试根据食材搜索
//        RecipeSearchDto dto = new RecipeSearchDto();
//        dto.setIngredientNames(Arrays.asList("鸡蛋", "包菜"));
//        dto.setPageNum(1);
//        dto.setPageSize(10);
//
//        ResultData<PageResult<RecipeListVo>> result = recipeService.searchRecipes(dto);
//        log.info("根据食材搜索结果：{}", JSONUtil.toJsonStr(result));
//    }
//
//    @Test
//    public void testSearchRecipesByDifficulty() {
//        // 测试根据难度等级搜索
//        RecipeSearchDto dto = new RecipeSearchDto();
//        dto.setDifficultyLevel(3);
//        dto.setPageNum(1);
//        dto.setPageSize(10);
//
//        ResultData<PageResult<RecipeListVo>> result = recipeService.searchRecipes(dto);
//        log.info("根据难度等级搜索结果：{}", JSONUtil.toJsonStr(result));
//    }
//
//    @Test
//    public void testGetRecipeDetail() {
//        // 测试获取菜谱详情
//        Integer recipeId = 1;
//        ResultData<RecipeDetailVo> result = recipeService.getRecipeDetail(recipeId);
//        log.info("菜谱详情：{}", JSONUtil.toJsonStr(result));
//
//        if (result.getData() != null) {
//            RecipeDetailVo detail = result.getData();
//            log.info("菜谱名称：{}", detail.getName());
//            log.info("难度等级：{}", detail.getDifficultyLevelDesc());
//            log.info("食材数量：{}", detail.getIngredients() != null ? detail.getIngredients().size() : 0);
//            log.info("步骤数量：{}", detail.getSteps() != null ? detail.getSteps().size() : 0);
//        }
//    }
//
//    @Test
//    public void testComplexSearch() {
//        // 测试复合条件搜索
//        RecipeSearchDto dto = new RecipeSearchDto();
//        dto.setRecipeName("炒");
//        dto.setDifficultyLevel(3);
//        dto.setPageNum(1);
//        dto.setPageSize(5);
//
//        ResultData<PageResult<RecipeListVo>> result = recipeService.searchRecipes(dto);
//        log.info("复合条件搜索结果：{}", JSONUtil.toJsonStr(result));
//    }
//}
