package com.cook.common.util;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @description ImageBase64Util测试类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/7/8
 */
public class ImageBase64UtilTest {

    /**
     * 测试MultipartFile转Base64
     */
    @Test
    public void testMultipartFileToBase64() throws IOException {
        // 创建模拟的图片文件
        byte[] imageBytes = createTestImageBytes();
        MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                imageBytes
        );

        // 转换为Base64
        String base64 = ImageBase64Util.multipartFileToBase64(mockFile);
        System.out.println("Base64编码长度: " + base64.length());
        System.out.println("Base64前50个字符: " + base64.substring(0, Math.min(50, base64.length())));

        // 验证转换结果
        assert base64 != null && !base64.isEmpty();
    }

    /**
     * 测试MultipartFile转Data URL
     */
    @Test
    public void testMultipartFileToDataUrl() throws IOException {
        byte[] imageBytes = createTestImageBytes();
        MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test.png",
                "image/png",
                imageBytes
        );

        String dataUrl = ImageBase64Util.multipartFileToDataUrl(mockFile);
        System.out.println("Data URL: " + dataUrl.substring(0, Math.min(100, dataUrl.length())) + "...");

        // 验证格式
        assert dataUrl.startsWith("data:image/png;base64,");
    }

    /**
     * 测试Base64转字节数组
     */
    @Test
    public void testBase64ToBytes() {
        // 测试普通Base64字符串
        String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        byte[] bytes = ImageBase64Util.base64ToBytes(base64);
        System.out.println("解码后字节数组长度: " + bytes.length);

        // 测试Data URL格式
        String dataUrl = "data:image/png;base64," + base64;
        byte[] bytesFromDataUrl = ImageBase64Util.base64ToBytes(dataUrl);
        System.out.println("从Data URL解码后字节数组长度: " + bytesFromDataUrl.length);

        // 验证结果一致
        assert bytes.length == bytesFromDataUrl.length;
    }

    /**
     * 测试Base64转InputStream
     */
    @Test
    public void testBase64ToInputStream() throws IOException {
        String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        try (InputStream inputStream = ImageBase64Util.base64ToInputStream(base64)) {
            int available = inputStream.available();
            System.out.println("InputStream可用字节数: " + available);
            assert available > 0;
        }
    }

    /**
     * 测试Base64转临时文件
     */
    @Test
    public void testBase64ToTempFile() throws IOException {
        String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        
        File tempFile = ImageBase64Util.base64ToTempFile(base64, "png");
        System.out.println("临时文件路径: " + tempFile.getAbsolutePath());
        System.out.println("临时文件大小: " + tempFile.length() + " bytes");
        
        assert tempFile.exists();
        assert tempFile.length() > 0;
        
        // 清理临时文件
        tempFile.deleteOnExit();
    }

    /**
     * 测试Base64保存为文件
     */
    @Test
    public void testBase64ToFile() throws IOException {
        String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        String filePath = System.getProperty("java.io.tmpdir") + "/test_image.png";
        
        ImageBase64Util.base64ToFile(base64, filePath);
        
        File file = new File(filePath);
        System.out.println("保存的文件路径: " + filePath);
        System.out.println("文件大小: " + file.length() + " bytes");
        
        assert file.exists();
        assert file.length() > 0;
        
        // 清理文件
        file.deleteOnExit();
    }

    /**
     * 测试验证Base64图片
     */
    @Test
    public void testIsValidImageBase64() {
        // 有效的PNG图片Base64
        String validBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        boolean isValid = ImageBase64Util.isValidImageBase64(validBase64);
        System.out.println("有效图片Base64验证结果: " + isValid);
        assert isValid;

        // 无效的Base64
        String invalidBase64 = "这不是一个有效的Base64字符串";
        boolean isInvalid = ImageBase64Util.isValidImageBase64(invalidBase64);
        System.out.println("无效Base64验证结果: " + isInvalid);
        assert !isInvalid;
    }

    /**
     * 测试提取图片格式
     */
    @Test
    public void testExtractImageFormat() {
        // 测试Data URL格式
        String dataUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";
        String format = ImageBase64Util.extractImageFormat(dataUrl);
        System.out.println("从Data URL提取的格式: " + format);
        assert "jpeg".equals(format);

        // 测试PNG格式
        String pngBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        String pngFormat = ImageBase64Util.extractImageFormat(pngBase64);
        System.out.println("PNG格式检测结果: " + pngFormat);
        assert "png".equals(pngFormat);
    }

    /**
     * 测试获取图片尺寸
     */
    @Test
    public void testGetImageDimensions() {
        // 1x1像素的PNG图片
        String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
        int[] dimensions = ImageBase64Util.getImageDimensions(base64);
        
        if (dimensions != null) {
            System.out.println("图片尺寸: " + dimensions[0] + "x" + dimensions[1]);
            assert dimensions[0] == 1 && dimensions[1] == 1;
        }
    }

    /**
     * 演示完整的转换流程
     */
    @Test
    public void testCompleteWorkflow() throws IOException {
        System.out.println("=== 完整转换流程演示 ===");
        
        // 1. 创建模拟文件
        byte[] imageBytes = createTestImageBytes();
        MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test.jpg",
                "image/jpeg",
                imageBytes
        );
        System.out.println("1. 创建模拟文件: " + mockFile.getOriginalFilename());

        // 2. 转换为Base64
        String base64 = ImageBase64Util.multipartFileToBase64(mockFile);
        System.out.println("2. 转换为Base64，长度: " + base64.length());

        // 3. 验证Base64
        boolean isValid = ImageBase64Util.isValidImageBase64(base64);
        System.out.println("3. Base64验证结果: " + isValid);

        // 4. 转换回字节数组
        byte[] decodedBytes = ImageBase64Util.base64ToBytes(base64);
        System.out.println("4. 解码后字节数组长度: " + decodedBytes.length);

        // 5. 创建临时文件
        File tempFile = ImageBase64Util.base64ToTempFile(base64, "jpg");
        System.out.println("5. 创建临时文件: " + tempFile.getName());

        // 6. 验证文件大小
        System.out.println("6. 原始字节数组长度: " + imageBytes.length);
        System.out.println("   解码字节数组长度: " + decodedBytes.length);
        System.out.println("   临时文件大小: " + tempFile.length());

        // 清理
        tempFile.deleteOnExit();
        
        System.out.println("=== 转换流程完成 ===");
    }

    /**
     * 创建测试用的图片字节数组（1x1像素的PNG）
     */
    private byte[] createTestImageBytes() {
        // 这是一个1x1像素的透明PNG图片的字节数组
        return new byte[] {
            (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
            0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
            0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
            0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, (byte) 0xC4,
            (byte) 0x89, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x44, 0x41, 0x54,
            0x78, (byte) 0xDA, 0x63, 0x60, (byte) 0x80, 0x01, 0x00,
            0x00, 0x05, 0x00, 0x01, 0x0D, (byte) 0x0A, 0x2D, (byte) 0xB4,
            0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44,
            (byte) 0xAE, 0x42, 0x60, (byte) 0x82
        };
    }
}
