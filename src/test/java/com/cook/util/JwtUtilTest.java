//package com.cook.util;
//
//import com.cook.common.util.JwtUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.Date;
//
///**
// * <AUTHOR>
// * @description JWT工具类测试
// * @copyright <铜豌豆-1forall.cn>
// * @since 2025/6/25
// */
//@Slf4j
//@SpringBootTest
//public class JwtUtilTest {
//
//    @Autowired
//    private JwtUtil jwtUtil;
//
//    @Test
//    public void testGenerateAndValidateToken() {
//        // 测试数据
//        Long userId = 123L;
//        String openid = "test_openid_123";
//        String nickname = "测试用户";
//
//        // 生成token
//        String token = jwtUtil.generateToken(userId, openid, nickname);
//        log.info("生成的JWT token: {}", token);
//
//        // 验证token
//        boolean isValid = jwtUtil.validateToken(token);
//        log.info("Token验证结果: {}", isValid);
//
//        // 从token中获取信息
//        Long extractedUserId = jwtUtil.getUserIdFromToken(token);
//        String extractedOpenid = jwtUtil.getOpenidFromToken(token);
//        String extractedNickname = jwtUtil.getNicknameFromToken(token);
//        Date expirationDate = jwtUtil.getExpirationDateFromToken(token);
//
//        log.info("从token中提取的用户ID: {}", extractedUserId);
//        log.info("从token中提取的openid: {}", extractedOpenid);
//        log.info("从token中提取的昵称: {}", extractedNickname);
//        log.info("Token过期时间: {}", expirationDate);
//
//        // 断言验证
//        assert isValid;
//        assert userId.equals(extractedUserId);
//        assert openid.equals(extractedOpenid);
//        assert nickname.equals(extractedNickname);
//        assert expirationDate != null;
//        assert expirationDate.after(new Date());
//    }
//
//    @Test
//    public void testRefreshToken() {
//        // 生成原始token
//        Long userId = 456L;
//        String openid = "test_openid_456";
//        String nickname = "刷新测试用户";
//
//        String originalToken = jwtUtil.generateToken(userId, openid, nickname);
//        log.info("原始token: {}", originalToken);
//
//        // 等待1秒确保时间戳不同
//        try {
//            Thread.sleep(1000);
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//        }
//
//        // 刷新token
//        String refreshedToken = jwtUtil.refreshToken(originalToken);
//        log.info("刷新后的token: {}", refreshedToken);
//
//        // 验证刷新后的token
//        assert refreshedToken != null;
//        assert !originalToken.equals(refreshedToken);
//        assert jwtUtil.validateToken(refreshedToken);
//        assert userId.equals(jwtUtil.getUserIdFromToken(refreshedToken));
//        assert openid.equals(jwtUtil.getOpenidFromToken(refreshedToken));
//        assert nickname.equals(jwtUtil.getNicknameFromToken(refreshedToken));
//    }
//
//    @Test
//    public void testInvalidToken() {
//        // 测试无效token
//        String invalidToken = "invalid.jwt.token";
//        boolean isValid = jwtUtil.validateToken(invalidToken);
//        log.info("无效token验证结果: {}", isValid);
//
//        assert !isValid;
//        assert jwtUtil.getUserIdFromToken(invalidToken) == null;
//        assert jwtUtil.getOpenidFromToken(invalidToken) == null;
//        assert jwtUtil.getNicknameFromToken(invalidToken) == null;
//    }
//}
