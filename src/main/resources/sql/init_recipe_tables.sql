-- 初始化菜谱相关表
-- 如果表已存在则不创建

-- 菜谱表
CREATE TABLE IF NOT EXISTS cook_recipe (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '菜名',
    description TEXT COMMENT '简介',
    difficulty_level TINYINT COMMENT '难度等级（1-3）',
    servings INT DEFAULT 1 COMMENT '份数',
    category INT DEFAULT 0 NOT NULL COMMENT '菜品分类：0-未知 1-荤菜 2-蔬菜 3-汤 4-海鲜 5-甜点 6-饮品 7-主食 8-调料 9-早餐'
);

-- 食材（包括调料）表
CREATE TABLE IF NOT EXISTS cook_ingredient (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '食材名，如"包菜"、"盐"、"鸡蛋"'
);

-- 菜谱-食材映射表
CREATE TABLE IF NOT EXISTS cook_recipe_ingredient (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipe_id INT NOT NULL COMMENT '菜谱ID',
    ingredient_id INT NOT NULL COMMENT '食材（包括调料）表ID',
    quantity VARCHAR(50) COMMENT '数量（如"半颗"、"2个"、"10ml"等，统一存为字符串便于表达）',
    type ENUM('main', 'supplement', 'seasoning') DEFAULT 'main' COMMENT '主料/辅料/调料',
    FOREIGN KEY (recipe_id) REFERENCES cook_recipe(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES cook_ingredient(id) ON DELETE CASCADE
);

-- 制作步骤表
CREATE TABLE IF NOT EXISTS cook_recipe_step (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipe_id INT NOT NULL COMMENT '菜谱ID',
    step_order INT NOT NULL COMMENT '步骤顺序',
    description TEXT COMMENT '步骤描述',
    duration_seconds INT COMMENT '可选：持续秒数',
    FOREIGN KEY (recipe_id) REFERENCES cook_recipe(id) ON DELETE CASCADE
);

-- 插入一些基础食材（如果不存在）
INSERT IGNORE INTO cook_ingredient (name) VALUES
('菜心'), ('生抽'), ('蚝油'), ('盐'), ('糖'), ('食用油'), 
('大蒜'), ('小米辣'), ('清水'), ('包菜'), ('鸡蛋'), ('粉丝'), 
('胡萝卜'), ('菜籽油'), ('老抽'), ('葱'), ('蒜'), ('干辣椒'),
('姜'), ('洋葱'), ('香菜'), ('韭菜'), ('豆腐'), ('猪肉'),
('牛肉'), ('鸡肉'), ('虾'), ('鱼'), ('土豆'), ('番茄'),
('青椒'), ('红椒'), ('黄瓜'), ('白萝卜'), ('胡萝卜'),
('豆角'), ('茄子'), ('冬瓜'), ('南瓜'), ('玉米'),
('料酒'), ('醋'), ('花椒'), ('八角'), ('桂皮'),
('香叶'), ('草果'), ('丁香'), ('白胡椒'), ('黑胡椒'),
('孜然'), ('辣椒粉'), ('五香粉'), ('鸡精'), ('味精');
