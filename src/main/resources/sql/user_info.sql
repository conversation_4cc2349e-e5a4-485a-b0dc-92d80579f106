-- 用户信息表
CREATE TABLE `user_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `openid` varchar(128) NOT NULL COMMENT '微信小程序openid',
  `unionid` varchar(128) DEFAULT NULL COMMENT '微信小程序unionid（可选）',
  `nickname` varchar(256) DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(1024) DEFAULT NULL COMMENT '用户头像URL',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `language` varchar(20) DEFAULT NULL COMMENT '语言',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT 0 COMMENT '用户状态：0-正常，1-禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';


-- 修改CookVersionRecord表，添加外键关联
ALTER TABLE `cook_version_record` 
MODIFY COLUMN `user_id` bigint(20) NOT NULL COMMENT '用户ID',
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_create_time` (`create_time`);
