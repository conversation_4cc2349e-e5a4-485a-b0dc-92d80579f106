<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多文件上传测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        
        .upload-section:hover {
            border-color: #007bff;
        }
        
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .upload-btn:hover {
            background: #0056b3;
        }
        
        .file-list {
            margin: 20px 0;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px 0;
            background: #f9f9f9;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
        }
        
        .file-size {
            color: #666;
            font-size: 14px;
        }
        
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .remove-btn:hover {
            background: #c82333;
        }
        
        .options {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .option-group {
            margin: 10px 0;
        }
        
        .option-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .option-group input, .option-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
        }
        
        .checkbox-group input {
            width: auto;
            margin-right: 8px;
        }
        
        .submit-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            background: #218838;
        }
        
        .submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .result-section {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
        }
        
        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        
        .ingredient-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .ingredient-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }
        
        .ingredient-name {
            font-weight: bold;
            color: #333;
        }
        
        .ingredient-rate {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍳 多文件上传测试页面</h1>
        
        <div class="upload-section" id="uploadSection">
            <p>📁 拖拽文件到此处或点击选择文件</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择文件
            </button>
            <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
            <p style="color: #666; font-size: 14px; margin-top: 10px;">
                支持 JPG、PNG、GIF、BMP、WebP 格式，单次最多上传10个文件
            </p>
        </div>
        
        <div class="file-list" id="fileList"></div>
        
        <div class="options">
            <div class="option-group">
                <label for="description">描述信息（可选）：</label>
                <textarea id="description" rows="3" placeholder="请输入对这批文件的描述..."></textarea>
            </div>
            
            <div class="option-group checkbox-group">
                <input type="checkbox" id="enableAiRecognition" checked>
                <label for="enableAiRecognition">启用AI食材识别</label>
            </div>
        </div>
        
        <button class="submit-btn" id="submitBtn" onclick="uploadFiles()" disabled>
            🚀 开始上传并识别
        </button>
        
        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="resultSection"></div>
    </div>

    <script>
        let selectedFiles = [];
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });
        
        // 拖拽处理
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        function handleFiles(files) {
            for (let file of files) {
                if (file.type.startsWith('image/')) {
                    selectedFiles.push(file);
                } else {
                    alert(`文件 ${file.name} 不是图片格式，已跳过`);
                }
            }
            
            if (selectedFiles.length > 10) {
                alert('最多只能选择10个文件，已自动截取前10个');
                selectedFiles = selectedFiles.slice(0, 10);
            }
            
            updateFileList();
            updateSubmitButton();
        }
        
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <button class="remove-btn" onclick="removeFile(${index})">删除</button>
                `;
                fileList.appendChild(fileItem);
            });
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateSubmitButton();
        }
        
        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = selectedFiles.length === 0;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        async function uploadFiles() {
            if (selectedFiles.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            const submitBtn = document.getElementById('submitBtn');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const resultSection = document.getElementById('resultSection');
            
            // 显示进度条
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            submitBtn.disabled = true;
            submitBtn.textContent = '上传中...';
            resultSection.innerHTML = '';
            
            try {
                // 构建FormData
                const formData = new FormData();
                selectedFiles.forEach(file => {
                    formData.append('files', file);
                });
                
                const description = document.getElementById('description').value;
                const enableAiRecognition = document.getElementById('enableAiRecognition').checked;
                
                if (description) {
                    formData.append('description', description);
                }
                formData.append('enableAiRecognition', enableAiRecognition);
                
                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                }, 200);
                
                // 发送请求
                const response = await fetch('/cook/upload/photos/multipart', {
                    method: 'POST',
                    body: formData
                });
                
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                const result = await response.json();
                
                // 显示结果
                if (result.code === 0) {
                    showResult(true, '上传成功！', result.data, result.msg);
                } else {
                    showResult(false, '上传失败', null, result.msg);
                }
                
            } catch (error) {
                console.error('上传错误：', error);
                showResult(false, '上传失败', null, '网络错误：' + error.message);
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 开始上传并识别';
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 2000);
            }
        }
        
        function showResult(success, title, data, message) {
            const resultSection = document.getElementById('resultSection');
            resultSection.className = `result-section ${success ? 'result-success' : 'result-error'}`;
            
            let html = `<h3>${title}</h3>`;
            if (message) {
                html += `<p><strong>消息：</strong>${message}</p>`;
            }
            
            if (success && data && data.length > 0) {
                html += `<p><strong>识别到 ${data.length} 种食材：</strong></p>`;
                html += '<div class="ingredient-list">';
                data.forEach(ingredient => {
                    html += `
                        <div class="ingredient-item">
                            <div class="ingredient-name">${ingredient.name}</div>
                            <div class="ingredient-rate">置信度: ${ingredient.rate}</div>
                        </div>
                    `;
                });
                html += '</div>';
            } else if (success && (!data || data.length === 0)) {
                html += '<p>未识别到食材，请尝试上传更清晰的食材照片。</p>';
            }
            
            resultSection.innerHTML = html;
        }
    </script>
</body>
</html>
