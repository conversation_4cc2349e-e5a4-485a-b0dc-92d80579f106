<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImageBase64Util 工具类使用示例</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #007bff;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        .method-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .method-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .method-desc {
            color: #666;
            margin-bottom: 15px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            background: #e3f2fd;
            margin: 5px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #2196f3;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .param-type {
            color: #007bff;
            font-weight: bold;
        }
        
        .return-type {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ ImageBase64Util 工具类使用指南</h1>
        
        <div class="highlight">
            <strong>📋 工具类概述：</strong>ImageBase64Util 是一个专门用于处理图片文件与Base64编码之间转换的工具类，支持多种图片格式和转换方式。
        </div>

        <h2>🚀 主要功能特性</h2>
        <ul class="feature-list">
            <li>MultipartFile ↔ Base64 双向转换</li>
            <li>支持Data URL格式（data:image/...;base64,...）</li>
            <li>Base64转换为文件流、临时文件、永久文件</li>
            <li>图片格式自动检测和验证</li>
            <li>图片尺寸信息获取</li>
            <li>支持多种图片格式：JPG、PNG、GIF、BMP、WebP</li>
            <li>完善的异常处理和日志记录</li>
        </ul>

        <h2>📚 API 方法详解</h2>

        <div class="method-section">
            <div class="method-title">1. multipartFileToBase64(MultipartFile file)</div>
            <div class="method-desc">将MultipartFile转换为Base64编码字符串（不包含data:image前缀）</div>
            <div class="code-block">
// 使用示例
MultipartFile file = ...; // 从前端上传的文件
String base64 = ImageBase64Util.multipartFileToBase64(file);
System.out.println("Base64: " + base64);
            </div>
            <table>
                <tr><th>参数</th><th>类型</th><th>说明</th></tr>
                <tr><td>file</td><td class="param-type">MultipartFile</td><td>图片文件</td></tr>
                <tr><th>返回值</th><th>类型</th><th>说明</th></tr>
                <tr><td>base64</td><td class="return-type">String</td><td>Base64编码字符串</td></tr>
            </table>
        </div>

        <div class="method-section">
            <div class="method-title">2. multipartFileToDataUrl(MultipartFile file)</div>
            <div class="method-desc">将MultipartFile转换为Base64数据URL格式（包含data:image前缀）</div>
            <div class="code-block">
// 使用示例
MultipartFile file = ...; 
String dataUrl = ImageBase64Util.multipartFileToDataUrl(file);
// 输出: data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">3. base64ToBytes(String base64String)</div>
            <div class="method-desc">将Base64编码字符串转换为字节数组，自动处理Data URL格式</div>
            <div class="code-block">
// 支持普通Base64格式
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
byte[] bytes = ImageBase64Util.base64ToBytes(base64);

// 也支持Data URL格式
String dataUrl = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
byte[] bytes2 = ImageBase64Util.base64ToBytes(dataUrl);
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">4. base64ToInputStream(String base64String)</div>
            <div class="method-desc">将Base64编码字符串转换为InputStream</div>
            <div class="code-block">
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
try (InputStream inputStream = ImageBase64Util.base64ToInputStream(base64)) {
    // 使用输入流处理图片数据
    BufferedImage image = ImageIO.read(inputStream);
}
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">5. base64ToTempFile(String base64String, String fileExtension)</div>
            <div class="method-desc">将Base64编码字符串转换为临时文件</div>
            <div class="code-block">
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
File tempFile = ImageBase64Util.base64ToTempFile(base64, "png");
System.out.println("临时文件路径: " + tempFile.getAbsolutePath());

// 记得在使用完后清理临时文件
tempFile.deleteOnExit();
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">6. base64ToFile(String base64String, String filePath)</div>
            <div class="method-desc">将Base64编码字符串保存为指定路径的文件</div>
            <div class="code-block">
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
String filePath = "/path/to/save/image.png";
ImageBase64Util.base64ToFile(base64, filePath);
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">7. fileToBase64(String filePath)</div>
            <div class="method-desc">从文件路径读取图片并转换为Base64编码</div>
            <div class="code-block">
String filePath = "/path/to/image.jpg";
String base64 = ImageBase64Util.fileToBase64(filePath);
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">8. isValidImageBase64(String base64String)</div>
            <div class="method-desc">验证Base64字符串是否为有效的图片</div>
            <div class="code-block">
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
boolean isValid = ImageBase64Util.isValidImageBase64(base64);
if (isValid) {
    System.out.println("这是一个有效的图片Base64编码");
}
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">9. extractImageFormat(String base64String)</div>
            <div class="method-desc">从Base64字符串中提取图片格式</div>
            <div class="code-block">
// 从Data URL中提取
String dataUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...";
String format = ImageBase64Util.extractImageFormat(dataUrl);
// 返回: "jpeg"

// 从普通Base64中检测
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
String format2 = ImageBase64Util.extractImageFormat(base64);
// 返回: "png"
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">10. getImageDimensions(String base64String)</div>
            <div class="method-desc">获取Base64编码图片的尺寸信息</div>
            <div class="code-block">
String base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...";
int[] dimensions = ImageBase64Util.getImageDimensions(base64);
if (dimensions != null) {
    int width = dimensions[0];
    int height = dimensions[1];
    System.out.println("图片尺寸: " + width + "x" + height);
}
            </div>
        </div>

        <h2>💡 实际应用场景</h2>

        <div class="method-section">
            <div class="method-title">场景1: 前端文件上传处理</div>
            <div class="code-block">
@PostMapping("/upload")
public ResultData&lt;String&gt; uploadImage(@RequestParam("file") MultipartFile file) {
    try {
        // 验证是否为有效图片
        String base64 = ImageBase64Util.multipartFileToBase64(file);
        if (!ImageBase64Util.isValidImageBase64(base64)) {
            return ResultData.fail("无效的图片文件");
        }
        
        // 获取图片信息
        String format = ImageBase64Util.extractImageFormat(base64);
        int[] dimensions = ImageBase64Util.getImageDimensions(base64);
        
        // 保存到文件系统
        String fileName = "upload_" + System.currentTimeMillis() + "." + format;
        String filePath = "/uploads/" + fileName;
        ImageBase64Util.base64ToFile(base64, filePath);
        
        return ResultData.success("上传成功: " + fileName);
    } catch (Exception e) {
        return ResultData.fail("上传失败: " + e.getMessage());
    }
}
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">场景2: 图片格式转换</div>
            <div class="code-block">
// 将用户上传的图片转换为统一格式存储
public String convertImageFormat(MultipartFile file, String targetFormat) throws IOException {
    // 转换为Base64
    String base64 = ImageBase64Util.multipartFileToBase64(file);
    
    // 创建临时文件
    File tempFile = ImageBase64Util.base64ToTempFile(base64, targetFormat);
    
    // 读取并重新编码为目标格式
    BufferedImage image = ImageIO.read(tempFile);
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    ImageIO.write(image, targetFormat, baos);
    
    // 清理临时文件
    tempFile.delete();
    
    return Base64.encode(baos.toByteArray());
}
            </div>
        </div>

        <div class="method-section">
            <div class="method-title">场景3: 批量图片处理</div>
            <div class="code-block">
public List&lt;String&gt; processBatchImages(MultipartFile[] files) {
    List&lt;String&gt; results = new ArrayList&lt;&gt;();
    
    for (MultipartFile file : files) {
        try {
            String base64 = ImageBase64Util.multipartFileToBase64(file);
            
            // 验证图片
            if (!ImageBase64Util.isValidImageBase64(base64)) {
                results.add("无效图片: " + file.getOriginalFilename());
                continue;
            }
            
            // 获取图片信息
            int[] dimensions = ImageBase64Util.getImageDimensions(base64);
            String format = ImageBase64Util.extractImageFormat(base64);
            
            results.add(String.format("处理成功: %s [%dx%d, %s]", 
                file.getOriginalFilename(), dimensions[0], dimensions[1], format));
                
        } catch (Exception e) {
            results.add("处理失败: " + file.getOriginalFilename() + " - " + e.getMessage());
        }
    }
    
    return results;
}
            </div>
        </div>

        <h2>⚠️ 注意事项</h2>
        <div class="warning">
            <strong>文件大小限制：</strong>工具类默认限制单个图片文件最大10MB，超过此大小会抛出异常。
        </div>
        
        <div class="warning">
            <strong>临时文件清理：</strong>使用 base64ToTempFile() 方法创建的临时文件需要手动清理，建议使用 file.deleteOnExit() 或在finally块中删除。
        </div>
        
        <div class="warning">
            <strong>内存使用：</strong>处理大图片时会占用较多内存，建议在生产环境中监控内存使用情况。
        </div>

        <h2>✅ 最佳实践</h2>
        <div class="success">
            <strong>1. 异常处理：</strong>始终使用try-catch块包装转换操作<br>
            <strong>2. 资源管理：</strong>及时关闭InputStream和清理临时文件<br>
            <strong>3. 格式验证：</strong>在处理前验证图片格式和有效性<br>
            <strong>4. 日志记录：</strong>工具类已内置日志，可通过日志级别控制输出
        </div>

        <div class="highlight">
            <strong>🔗 相关文件：</strong><br>
            • 工具类源码：<code>src/main/java/com/cook/common/util/ImageBase64Util.java</code><br>
            • 测试用例：<code>src/test/java/com/cook/common/util/ImageBase64UtilTest.java</code><br>
            • 使用示例：<code>src/main/java/com/cook/controller/CookInfoController.java</code>
        </div>
    </div>
</body>
</html>
