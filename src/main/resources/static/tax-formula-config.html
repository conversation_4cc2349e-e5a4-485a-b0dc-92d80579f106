<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>税费计算公式配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
        }

        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .content-area {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.4em;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .param-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        .param-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .param-item:hover {
            background-color: #f8f9ff;
            transform: translateX(5px);
        }

        .param-item:last-child {
            border-bottom: none;
        }

        .param-code {
            font-family: 'Courier New', monospace;
            background-color: #f1f3f4;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #1a73e8;
        }

        .param-desc {
            color: #666;
            font-size: 0.95em;
        }

        .formula-editor {
            margin-bottom: 25px;
        }

        .formula-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            resize: vertical;
            min-height: 80px;
            transition: border-color 0.3s ease;
        }

        .formula-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .formula-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }

        .formula-chinese {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #**********%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .tax-config-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tax-config-table th,
        .tax-config-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .tax-config-table th {
            background-color: #667eea;
            color: white;
            font-weight: 600;
        }

        .tax-config-table tr:hover {
            background-color: #f8f9ff;
        }

        .formula-cell {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-width: 200px;
            word-break: break-all;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.85em;
        }

        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            animation: highlight 2s ease-in-out;
        }

        @keyframes highlight {
            0% { background-color: #fff3cd; }
            100% { background-color: transparent; }
        }

        .search-box {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 0.95em;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 税费计算公式配置管理</h1>
            <p>公式配置与参数管理系统</p>
        </div>

        <div class="main-content">
            <!-- 左侧参数列表 -->
            <div class="sidebar">
                <h3 class="section-title">📋 参数列表</h3>
                <input type="text" class="search-box" id="paramSearch" placeholder="搜索参数...">
                <div class="param-list" id="paramList">
                    <!-- 参数列表将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 右侧公式编辑区 -->
            <div class="content-area">
                <h3 class="section-title">⚙️ 公式编辑器</h3>
                
                <div class="formula-editor">
                    <label for="formulaInput"><strong>公式编辑：</strong></label>
                    <textarea id="formulaInput" class="formula-input" placeholder="请输入或编辑税务计算公式，格式：参数名=计算公式">taxAmount=taxBasis*taxRate</textarea>
                </div>

                <div class="formula-display">
                    <strong>当前公式：</strong>
                    <div id="formulaDisplay">taxAmount=taxBasis*taxRate</div>
                </div>

                <div class="formula-chinese">
                    <strong>中文解释：</strong>
                    <div id="formulaChinese">应纳税额 = 计税依据 × 税率</div>
                </div>

                <div style="margin-bottom: 30px;">
                    <button class="btn" onclick="updateFormula()">💾 更新公式</button>
                    <button class="btn btn-secondary" onclick="clearFormula()">🗑️ 清空</button>
                    <button class="btn btn-secondary" onclick="validateFormula()">✅ 验证公式</button>
                </div>

                <h3 class="section-title">📊 税务配置表</h3>

                <!-- 筛选条件区域 -->
                <div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                        <label style="font-weight: 600; color: #495057;">🔍 筛选条件：</label>

                        <div style="display: flex; align-items: center; gap: 8px;">
                            <label for="taxTypeFilter" style="font-size: 0.95em; color: #6c757d;">税种：</label>
                            <select id="taxTypeFilter" style="padding: 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.9em;">
                                <option value="">全部税种</option>
                                <option value="1">个人所得税</option>
                                <option value="2">增值税</option>
                                <option value="3">城市维护建设税</option>
                                <option value="4">教育附加税</option>
                                <option value="5">地方教育附加税</option>
                                <option value="6">附加税</option>
                            </select>
                        </div>

                        <div style="display: flex; align-items: center; gap: 8px;">
                            <label for="assessmentTypeFilter" style="font-size: 0.95em; color: #6c757d;">计税类型：</label>
                            <select id="assessmentTypeFilter" style="padding: 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.9em;">
                                <option value="">全部类型</option>
                                <option value="1">应纳税额</option>
                                <option value="2">减免税额</option>
                                <option value="3">已缴税额</option>
                                <option value="4">应代征税额</option>
                                <option value="5">已代征税额</option>
                                <option value="6">计税依据</option>
                                <option value="7">增值税小规模纳税人减征额</option>
                            </select>
                        </div>

                        <div style="display: flex; align-items: center; gap: 8px;">
                            <label for="formulaSearch" style="font-size: 0.95em; color: #6c757d;">公式搜索：</label>
                            <input type="text" id="formulaSearch" placeholder="搜索公式内容..." style="padding: 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.9em; width: 200px;">
                        </div>

                        <button class="btn btn-secondary btn-small" onclick="clearFilters()" style="margin-left: auto;">🗑️ 清空筛选</button>
                    </div>
                </div>

                <table class="tax-config-table" id="taxConfigTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>税种</th>
                            <th>计税区间</th>
                            <th>计税类型</th>
                            <th>计算公式</th>
                            <th>中文公式</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="taxConfigBody">
                        <!-- 表格数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 参数映射表
        const paramMapping = {
            'taxAmount': '应纳税额',
            'taxReduction': '减免税额',
            'paymentedTaxAmount': '已缴税额',
            'shouldSubstitutedTax': '应代征税额',
            'substitutedTax': '已代征税额',
            'taxBasis': '计税依据',
            'vaTSmallScaleAmount': '增值税小规模纳税人减征额',
            'payAmount': '支付金额',
            'taxRate': '税率',
            'taxBaseRate': '计税基础税率',
            'taxRaduceRate': '减征税率'
        };

        // 税种映射
        const taxTypeMapping = {
            1: '个人所得税',
            2: '增值税',
            3: '城市维护建设税',
            4: '教育附加税',
            5: '地方教育附加税',
            6: '附加税'
        };

        // 计税类型映射
        const taxAssessmentMapping = {
            1: '应纳税额',
            2: '减免税额',
            3: '已缴税额',
            4: '应代征税额',
            5: '已代征税额',
            6: '计税依据',
            7: '增值税小规模纳税人减征额'
        };

        // 示例数据
        const sampleData = [
            {id: 1823, taxType: 1, sectionType: 21, assessmentType: 5, formula: '(taxAmount-taxReduction)*0'},
            {id: 1824, taxType: 1, sectionType: 21, assessmentType: 4, formula: 'taxBasis*taxRate'},
            {id: 1825, taxType: 1, sectionType: 21, assessmentType: 3, formula: 'payAmount*0'},
            {id: 1826, taxType: 1, sectionType: 21, assessmentType: 2, formula: 'taxAmount*0'},
            {id: 1827, taxType: 1, sectionType: 21, assessmentType: 1, formula: 'taxBasis*taxRate'},
            {id: 1828, taxType: 1, sectionType: 21, assessmentType: 6, formula: 'payAmount*1'},
            {id: 1829, taxType: 2, sectionType: 21, assessmentType: 2, formula: 'taxAmount-shouldSubstitutedTax'},
            {id: 1830, taxType: 2, sectionType: 21, assessmentType: 4, formula: 'taxBasis*0.01'},
            {id: 1831, taxType: 2, sectionType: 21, assessmentType: 3, formula: 'payAmount*0'},
            {id: 1832, taxType: 2, sectionType: 21, assessmentType: 1, formula: 'taxBasis*taxRate'},
            {id: 1833, taxType: 2, sectionType: 21, assessmentType: 6, formula: 'payAmount*1'},
            {id: 1834, taxType: 2, sectionType: 21, assessmentType: 5, formula: 'taxBasis*0.01*0'},
            {id: 1835, taxType: 3, sectionType: 21, assessmentType: 6, formula: 'payAmount*taxBaseRate'},
            {id: 1836, taxType: 3, sectionType: 21, assessmentType: 1, formula: 'taxBasis*taxRate'},
            {id: 1837, taxType: 3, sectionType: 21, assessmentType: 2, formula: 'taxAmount*0'},
            {id: 1838, taxType: 3, sectionType: 21, assessmentType: 3, formula: 'payAmount*0'},
            {id: 1839, taxType: 3, sectionType: 21, assessmentType: 4, formula: 'taxAmount-vaTSmallScaleAmount'},
            {id: 1840, taxType: 3, sectionType: 21, assessmentType: 5, formula: '(taxAmount-vaTSmallScaleAmount)*0'},
            {id: 1841, taxType: 3, sectionType: 21, assessmentType: 7, formula: '(taxAmount-taxReduction)*taxRaduceRate'},
            {id: 1842, taxType: 4, sectionType: 21, assessmentType: 1, formula: 'taxBasis*taxRate'},
            {id: 1843, taxType: 4, sectionType: 21, assessmentType: 7, formula: '(taxAmount-taxReduction)*taxRaduceRate'},
            {id: 1844, taxType: 4, sectionType: 21, assessmentType: 5, formula: '(taxAmount-vaTSmallScaleAmount)*0'},
            {id: 1845, taxType: 4, sectionType: 21, assessmentType: 4, formula: 'taxAmount-vaTSmallScaleAmount'},
            {id: 1846, taxType: 4, sectionType: 21, assessmentType: 3, formula: 'payAmount*0'},
            {id: 1847, taxType: 4, sectionType: 21, assessmentType: 2, formula: 'taxAmount*0'},
            {id: 1848, taxType: 4, sectionType: 21, assessmentType: 6, formula: 'payAmount*taxBaseRate'},
            {id: 1849, taxType: 5, sectionType: 21, assessmentType: 6, formula: 'payAmount*taxBaseRate'},
            {id: 1850, taxType: 5, sectionType: 21, assessmentType: 1, formula: 'taxBasis*taxRate'},
            {id: 1851, taxType: 5, sectionType: 21, assessmentType: 2, formula: 'taxAmount*0'},
            {id: 1852, taxType: 5, sectionType: 21, assessmentType: 3, formula: 'payAmount*0'},
            {id: 1853, taxType: 5, sectionType: 21, assessmentType: 4, formula: 'taxAmount-vaTSmallScaleAmount'},
            {id: 1854, taxType: 5, sectionType: 21, assessmentType: 5, formula: '(taxAmount-vaTSmallScaleAmount)*0'},
            {id: 1855, taxType: 5, sectionType: 21, assessmentType: 7, formula: '(taxAmount-taxReduction)*taxRaduceRate'}
        ];

        // 存储原始数据和过滤后的数据
        let originalData = [...sampleData];
        let filteredData = [...sampleData];

        // 初始化页面
        function initPage() {
            renderParamList();
            renderTaxConfigTable();
            updateFormulaDisplay();
            initFilterEvents();
        }

        // 渲染参数列表
        function renderParamList() {
            const paramList = document.getElementById('paramList');
            paramList.innerHTML = '';
            
            Object.entries(paramMapping).forEach(([code, desc]) => {
                const paramItem = document.createElement('div');
                paramItem.className = 'param-item';
                paramItem.onclick = () => insertParam(code);
                paramItem.innerHTML = `
                    <span class="param-code">${code}</span>
                    <span class="param-desc">${desc}</span>
                `;
                paramList.appendChild(paramItem);
            });
        }

        // 插入参数到公式
        function insertParam(paramCode) {
            const formulaInput = document.getElementById('formulaInput');
            const cursorPos = formulaInput.selectionStart;
            const textBefore = formulaInput.value.substring(0, cursorPos);
            const textAfter = formulaInput.value.substring(formulaInput.selectionEnd);
            
            formulaInput.value = textBefore + paramCode + textAfter;
            formulaInput.focus();
            formulaInput.setSelectionRange(cursorPos + paramCode.length, cursorPos + paramCode.length);
            
            updateFormulaDisplay();
        }

        // 更新公式显示
        function updateFormulaDisplay() {
            const formula = document.getElementById('formulaInput').value;
            document.getElementById('formulaDisplay').textContent = formula;

            // 转换为中文
            let chineseFormula = formula;
            Object.entries(paramMapping).forEach(([code, desc]) => {
                const regex = new RegExp(code, 'g');
                chineseFormula = chineseFormula.replace(regex, desc);
            });

            // 替换运算符
            chineseFormula = chineseFormula
                .replace(/\*/g, ' × ')
                .replace(/\+/g, ' + ')
                .replace(/-/g, ' - ')
                .replace(/\//g, ' ÷ ')
                .replace(/\(/g, '（')
                .replace(/\)/g, '）')
                .replace(/=/g, ' = ');

            document.getElementById('formulaChinese').textContent = chineseFormula;
        }

        // 渲染税务配置表
        function renderTaxConfigTable() {
            const tbody = document.getElementById('taxConfigBody');
            tbody.innerHTML = '';

            filteredData.forEach(item => {
                const row = document.createElement('tr');

                // 转换公式为中文
                let chineseFormula = item.formula;
                Object.entries(paramMapping).forEach(([code, desc]) => {
                    const regex = new RegExp(code, 'g');
                    chineseFormula = chineseFormula.replace(regex, desc);
                });
                chineseFormula = chineseFormula
                    .replace(/\*/g, ' × ')
                    .replace(/\+/g, ' + ')
                    .replace(/-/g, ' - ')
                    .replace(/\//g, ' ÷ ');

                row.innerHTML = `
                    <td>${item.id}</td>
                    <td>${taxTypeMapping[item.taxType] || item.taxType}</td>
                    <td>${item.sectionType}</td>
                    <td>${taxAssessmentMapping[item.assessmentType] || item.assessmentType}</td>
                    <td class="formula-cell">${item.formula}</td>
                    <td class="formula-cell">${chineseFormula}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small" onclick="editFormula('${item.formula}', ${item.assessmentType})">编辑</button>
                        <button class="btn btn-small btn-danger" onclick="deleteConfig(${item.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 更新显示的记录数
            updateRecordCount();
        }

        // 编辑公式
        function editFormula(formula, assessmentType) {
            // 根据计税类型获取对应的参数名
            const assessmentTypeParam = getAssessmentTypeParam(assessmentType);

            // 格式化公式为 参数名=公式 的格式
            const formattedFormula = `${assessmentTypeParam}=${formula}`;

            document.getElementById('formulaInput').value = formattedFormula;
            updateFormulaDisplay();

            // 滚动到编辑器
            document.getElementById('formulaInput').scrollIntoView({ behavior: 'smooth' });
            document.getElementById('formulaInput').focus();
        }

        // 根据计税类型获取对应的参数名
        function getAssessmentTypeParam(assessmentType) {
            const assessmentTypeParams = {
                1: 'taxAmount',           // 应纳税额
                2: 'taxReduction',        // 减免税额
                3: 'paymentedTaxAmount',  // 已缴税额
                4: 'shouldSubstitutedTax', // 应代征税额
                5: 'substitutedTax',      // 已代征税额
                6: 'taxBasis',            // 计税依据
                7: 'vaTSmallScaleAmount'  // 增值税小规模纳税人减征额
            };
            return assessmentTypeParams[assessmentType] || 'result';
        }

        // 更新公式
        function updateFormula() {
            updateFormulaDisplay();
            alert('公式已更新！');
        }

        // 清空公式
        function clearFormula() {
            document.getElementById('formulaInput').value = '';
            updateFormulaDisplay();
        }

        // 验证公式
        function validateFormula() {
            const formula = document.getElementById('formulaInput').value;

            if (!formula.trim()) {
                alert('❌ 公式不能为空！');
                return;
            }

            // 检查是否包含等号
            if (!formula.includes('=')) {
                alert('❌ 公式格式错误！请使用格式：参数名=计算公式');
                return;
            }

            // 分离等号左右两边
            const parts = formula.split('=');
            if (parts.length !== 2) {
                alert('❌ 公式格式错误！只能包含一个等号');
                return;
            }

            const leftSide = parts[0].trim();
            const rightSide = parts[1].trim();

            if (!leftSide || !rightSide) {
                alert('❌ 等号两边都不能为空！');
                return;
            }

            // 简单的语法验证（右边公式部分）
            const validChars = /^[a-zA-Z0-9\+\-\*\/\(\)\.\s]+$/;
            if (!validChars.test(rightSide)) {
                alert('❌ 公式包含无效字符！');
                return;
            }

            // 检查括号匹配
            const openBrackets = (rightSide.match(/\(/g) || []).length;
            const closeBrackets = (rightSide.match(/\)/g) || []).length;
            if (openBrackets !== closeBrackets) {
                alert('❌ 括号不匹配！');
                return;
            }

            alert('✅ 公式验证通过！');
        }

        // 删除配置
        function deleteConfig(id) {
            if (confirm('确定要删除这个配置吗？')) {
                alert(`配置 ${id} 已删除！`);
                // 这里可以添加实际的删除逻辑
            }
        }

        // 搜索参数
        document.getElementById('paramSearch').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const paramItems = document.querySelectorAll('.param-item');
            
            paramItems.forEach(item => {
                const code = item.querySelector('.param-code').textContent.toLowerCase();
                const desc = item.querySelector('.param-desc').textContent.toLowerCase();
                
                if (code.includes(searchTerm) || desc.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 监听公式输入变化
        document.getElementById('formulaInput').addEventListener('input', updateFormulaDisplay);

        // 初始化筛选事件
        function initFilterEvents() {
            // 税种筛选
            document.getElementById('taxTypeFilter').addEventListener('change', applyFilters);

            // 计税类型筛选
            document.getElementById('assessmentTypeFilter').addEventListener('change', applyFilters);

            // 公式搜索
            document.getElementById('formulaSearch').addEventListener('input', applyFilters);
        }

        // 应用筛选条件
        function applyFilters() {
            const taxTypeFilter = document.getElementById('taxTypeFilter').value;
            const assessmentTypeFilter = document.getElementById('assessmentTypeFilter').value;
            const formulaSearch = document.getElementById('formulaSearch').value.toLowerCase();

            filteredData = originalData.filter(item => {
                // 税种筛选
                if (taxTypeFilter && item.taxType.toString() !== taxTypeFilter) {
                    return false;
                }

                // 计税类型筛选
                if (assessmentTypeFilter && item.assessmentType.toString() !== assessmentTypeFilter) {
                    return false;
                }

                // 公式搜索
                if (formulaSearch && !item.formula.toLowerCase().includes(formulaSearch)) {
                    return false;
                }

                return true;
            });

            renderTaxConfigTable();
        }

        // 清空筛选条件
        function clearFilters() {
            document.getElementById('taxTypeFilter').value = '';
            document.getElementById('assessmentTypeFilter').value = '';
            document.getElementById('formulaSearch').value = '';

            filteredData = [...originalData];
            renderTaxConfigTable();
        }

        // 更新记录数显示
        function updateRecordCount() {
            const totalCount = originalData.length;
            const filteredCount = filteredData.length;

            // 在表格标题中显示记录数
            const sectionTitle = document.querySelector('.section-title');
            if (sectionTitle && sectionTitle.textContent.includes('📊')) {
                if (filteredCount === totalCount) {
                    sectionTitle.innerHTML = `📊 税务配置表 <span style="font-size: 0.8em; color: #6c757d; font-weight: normal;">(共 ${totalCount} 条记录)</span>`;
                } else {
                    sectionTitle.innerHTML = `📊 税务配置表 <span style="font-size: 0.8em; color: #6c757d; font-weight: normal;">(显示 ${filteredCount} / ${totalCount} 条记录)</span>`;
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
