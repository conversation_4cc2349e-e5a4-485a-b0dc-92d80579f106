<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #5a67d8;
            background-color: #f7fafc;
        }

        .upload-area.dragover {
            border-color: #4c51bf;
            background-color: #edf2f7;
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 0.9em;
            color: #718096;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result-area {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }

        .result-success {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #22543d;
        }

        .result-error {
            background-color: #fed7d7;
            border: 1px solid #fc8181;
            color: #742a2a;
        }

        .file-info {
            background-color: #edf2f7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }

        .file-info-item {
            margin: 5px 0;
            font-size: 0.9em;
        }

        .file-info-label {
            font-weight: 600;
            color: #4a5568;
        }

        .test-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .log-area {
            background-color: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #90cdf4;
        }

        .log-level-info {
            color: #68d391;
        }

        .log-level-error {
            color: #fc8181;
        }

        .log-level-warn {
            color: #f6e05e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 文件上传测试</h1>
            <p>测试百度OSS文件上传功能</p>
        </div>

        <!-- 上传区域 -->
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📁</div>
            <div class="upload-text">点击选择文件或拖拽文件到此处</div>
            <div class="upload-hint">支持图片、文档等格式，最大10MB</div>
        </div>

        <input type="file" id="fileInput" class="file-input" accept="*/*">

        <!-- 文件信息 -->
        <div class="file-info" id="fileInfo">
            <div class="file-info-item">
                <span class="file-info-label">文件名：</span>
                <span id="fileName">-</span>
            </div>
            <div class="file-info-item">
                <span class="file-info-label">文件大小：</span>
                <span id="fileSize">-</span>
            </div>
            <div class="file-info-item">
                <span class="file-info-label">文件类型：</span>
                <span id="fileType">-</span>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="progressText">0%</span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="test-buttons">
            <button class="btn" id="uploadBtn" onclick="uploadFile()" disabled>🚀 开始上传</button>
            <button class="btn" onclick="clearResult()">🗑️ 清空结果</button>
            <button class="btn" onclick="testAPI()">🧪 API测试</button>
        </div>

        <!-- 结果显示 -->
        <div class="result-area" id="resultArea">
            <div id="resultContent"></div>
        </div>

        <!-- 日志区域 -->
        <div class="log-area" id="logArea">
            <div class="log-entry">
                <span class="log-timestamp">[2025-07-08 11:35:00]</span>
                <span class="log-level-info">[INFO]</span>
                文件上传测试页面已加载
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            initUploadArea();
            addLog('页面初始化完成', 'info');
        });

        // 初始化上传区域
        function initUploadArea() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                displayFileInfo(file);
                addLog(`文件已选择: ${file.name}`, 'info');
            }
        }

        // 处理拖拽悬停
        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.add('dragover');
        }

        // 处理拖拽离开
        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        // 处理文件拖拽
        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                displayFileInfo(files[0]);
                addLog(`文件已拖拽选择: ${files[0].name}`, 'info');
            }
        }

        // 显示文件信息
        function displayFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = file.type || '未知';
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('uploadBtn').disabled = false;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 上传文件
        async function uploadFile() {
            if (!selectedFile) {
                addLog('请先选择文件', 'warn');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);

            // 显示进度条
            showProgress();
            addLog('开始上传文件...', 'info');

            try {
                const response = await fetch('/baidu-oss/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    showResult(true, '上传成功！', result);
                    addLog('文件上传成功', 'info');
                } else {
                    showResult(false, '上传失败', result);
                    addLog(`文件上传失败: ${result.msg || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(false, '网络错误', { error: error.message });
                addLog(`网络错误: ${error.message}`, 'error');
            } finally {
                hideProgress();
            }
        }

        // 显示进度条
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            // 模拟进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                }
                updateProgress(progress);
            }, 200);
        }

        // 更新进度
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = Math.round(percent) + '%';
        }

        // 隐藏进度条
        function hideProgress() {
            setTimeout(() => {
                document.getElementById('progressContainer').style.display = 'none';
                updateProgress(0);
            }, 1000);
        }

        // 显示结果
        function showResult(success, message, data) {
            const resultArea = document.getElementById('resultArea');
            const resultContent = document.getElementById('resultContent');
            
            resultArea.className = 'result-area ' + (success ? 'result-success' : 'result-error');
            resultArea.style.display = 'block';
            
            resultContent.innerHTML = `
                <h3>${success ? '✅' : '❌'} ${message}</h3>
                <pre style="margin-top: 10px; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        // 清空结果
        function clearResult() {
            document.getElementById('resultArea').style.display = 'none';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('fileInput').value = '';
            document.getElementById('uploadBtn').disabled = true;
            selectedFile = null;
            addLog('结果已清空', 'info');
        }

        // API测试
        function testAPI() {
            addLog('开始API连通性测试...', 'info');
            
            fetch('/baidu-oss/upload', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => response.text())
            .then(data => {
                addLog('API测试完成，检查是否返回multipart错误', 'info');
                console.log('API Test Response:', data);
            })
            .catch(error => {
                addLog(`API测试失败: ${error.message}`, 'error');
            });
        }

        // 添加日志
        function addLog(message, level = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
    </script>
</body>
</html>
