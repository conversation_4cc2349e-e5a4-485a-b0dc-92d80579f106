package com.cook.common.enums;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
public enum ApiCodeEnum {
    SUCCESS(0, "成功"),
    ERROR(1, "失败"),
    ;

    private int code;
    private String msg;
    private static Map<Integer, String> CODE_MAP = new HashMap<>(16);

    static {
        CODE_MAP = Arrays.stream(ApiCodeEnum.values())
                .collect(Collectors.toMap(x-> x.code, y->y.msg));
    }


    ApiCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMessage(ApiCodeEnum apiCodeEnum) {
        if (apiCodeEnum != null) {
            return CODE_MAP.get(apiCodeEnum.code);
        }
        return null;
    }

}