package com.cook.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 菜品分类枚举
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Getter
@AllArgsConstructor
public enum RecipeCategoryEnum {
    
    UNKNOWN(0, "未知", "unknown"),
    MEAT_DISH(1, "荤菜", "meat_dish"),
    VEGETABLE_DISH(2, "蔬菜", "vegetable_dish"),
    SOUP(3, "汤", "soup"),
    SEAFOOD(4, "海鲜", "seafood"),
    DESSERT(5, "甜点", "dessert"),
    BEVERAGE(6, "饮品", "beverage"),
    STAPLE_FOOD(7, "主食", "staple_food"),
    SEASONING(8, "调料", "seasoning"),
    BREAKFAST(9, "早餐", "breakfast");
    
    /**
     * 分类代码
     */
    private final Integer code;
    
    /**
     * 分类名称
     */
    private final String name;
    
    /**
     * 文件夹名称（对应HowToCook项目的文件夹）
     */
    private final String folderName;
    
    /**
     * 根据文件夹名称获取分类
     */
    public static RecipeCategoryEnum getByFolderName(String folderName) {
        if (folderName == null) {
            return UNKNOWN;
        }
        
        for (RecipeCategoryEnum category : values()) {
            if (category.getName().equals(folderName)) {
                return category;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 根据代码获取分类
     */
    public static RecipeCategoryEnum getByCode(Integer code) {
        if (code == null) {
            return UNKNOWN;
        }
        
        for (RecipeCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 根据名称获取分类
     */
    public static RecipeCategoryEnum getByName(String name) {
        if (name == null) {
            return UNKNOWN;
        }
        
        for (RecipeCategoryEnum category : values()) {
            if (category.getName().equals(name)) {
                return category;
            }
        }
        
        return UNKNOWN;
    }
}
