package com.cook.common.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cook.common.util.JwtUtil;
import com.cook.model.base.ResultData;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 * @description JWT拦截器
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class JwtInterceptor implements HandlerInterceptor {

    final JwtUtil jwtUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 跨域预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        // 获取token
//        String token = getTokenFromRequest(request);
//
//        if (StrUtil.isBlank(token)) {
//            writeErrorResponse(response, "未提供访问令牌");
//            return false;
//        }
//
//        // 验证token
//        if (!jwtUtil.validateToken(token)) {
//            writeErrorResponse(response, "访问令牌无效或已过期");
//            return false;
//        }
//
//        // 将用户信息存储到请求属性中，供后续使用
//        Long userId = jwtUtil.getUserIdFromToken(token);
//        String openid = jwtUtil.getOpenidFromToken(token);
//        String nickname = jwtUtil.getNicknameFromToken(token);
//
//        request.setAttribute("userId", userId);
//        request.setAttribute("openid", openid);
//        request.setAttribute("nickname", nickname);
//        request.setAttribute("token", token);

//        log.debug("JWT验证通过，用户ID: {}, openid: {}", userId, openid);
        return true;
    }

    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 从Header中获取token
        String bearerToken = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        // 从参数中获取token
        String paramToken = request.getParameter("token");
        if (StrUtil.isNotBlank(paramToken)) {
            return paramToken;
        }
        
        return null;
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        ResultData<Object> result = ResultData.fail(message);
        response.getWriter().write(JSONUtil.toJsonStr(result));
    }
}
