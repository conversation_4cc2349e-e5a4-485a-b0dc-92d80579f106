package com.cook.common.config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
//import springfox.documentation.builders.ApiInfoBuilder;
//import springfox.documentation.builders.PathSelectors;
//import springfox.documentation.builders.RequestHandlerSelectors;
//import springfox.documentation.service.ApiInfo;
//import springfox.documentation.service.Contact;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spring.web.plugins.Docket;
//import springfox.documentation.swagger2.annotations.EnableSwagger2;
/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/14 14:38
 */
//@EnableSwagger3
@Configuration
public class Swaggerconfig {

// @Bean
// public Docket createRestApi() {
//  return new Docket(DocumentationType.SWAGGER_2)
//          .apiInfo(apiInfo())
//          .select()
//          .apis(RequestHandlerSelectors.basePackage("com.cook.controller"))
//          .paths(PathSelectors.any())
//          .build();
// }
//
// private ApiInfo apiInfo() {
//  Contact contact = new Contact("王加军", "1forall.cn", "<EMAIL>");
//  return new ApiInfoBuilder()
//          .title("铜豌豆")
//          .description("接口文档")
//          .version("1.0.0")
//          .contact(contact)
//          .build();
// }
}