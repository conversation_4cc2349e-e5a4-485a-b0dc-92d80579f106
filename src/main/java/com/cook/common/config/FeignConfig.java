package com.cook.common.config;

import feign.Logger;
import feign.codec.Decoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description Feign配置类，处理微信API返回text/plain但实际是JSON的问题
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/23 20:29
 */
@Configuration
public class FeignConfig {

    @Bean
    Logger.Level feignLoggerLevel() {
        // 设置日志级别为FULL，记录请求和响应的头信息、正文和元数据
        return Logger.Level.FULL;
    }

    @Bean
    public Decoder feignDecoder() {
        // 创建支持text/plain的Jackson转换器
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(
                MediaType.APPLICATION_JSON,
                MediaType.TEXT_PLAIN, // 添加对text/plain的支持
                new MediaType("application", "*+json")));

        ObjectFactory<HttpMessageConverters> messageConverters = () -> new HttpMessageConverters(converter);

        return new ResponseEntityDecoder(new SpringDecoder(messageConverters));
    }
}
