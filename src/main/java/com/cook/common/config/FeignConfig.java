package com.cook.common.config;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/23 20:29
 */
@Configuration
public class FeignConfig {
    @Bean
    Logger.Level feignLoggerLevel() {
        // 设置日志级别为FULL，记录请求和响应的头信息、正文和元数据
        return Logger.Level.FULL;
    }
}
