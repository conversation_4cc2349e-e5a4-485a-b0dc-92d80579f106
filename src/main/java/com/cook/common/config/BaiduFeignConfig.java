package com.cook.common.config;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 百度API专用Feign配置
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Configuration
public class BaiduFeignConfig {

    @Bean
    Logger.Level baiduFeignLoggerLevel() {
        return Logger.Level.FULL;
    }
}
