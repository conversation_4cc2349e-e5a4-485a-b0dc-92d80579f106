package com.cook.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * <AUTHOR>
 * @description JWT配置类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "jwt")
public class JwtConfig {
    
    /**
     * JWT密钥
     */
    private String secret = "cook_jwt_secret_key_2025_very_long_secret_key_for_security";
    
    /**
     * JWT过期时间（毫秒）默认7天
     */
    private Long expiration = 604800000L;
    
    /**
     * JWT签发者
     */
    private String issuer = "cook-app";
    
    /**
     * JWT请求头名称
     */
    private String header = "Authorization";
    
    /**
     * JWT token前缀
     */
    private String tokenPrefix = "Bearer ";
}
