package com.cook.common.config;

import com.baidubce.BceClientConfiguration;
import com.baidubce.auth.BceCredentials;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.sts.StsClient;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Configuration
public class BaiduOssClientConfig {
    @Value("${baidu.oss.secret-key-id:}")
    private String ossAccessKeyId;
    @Value("${baidu.oss.access-key-secret:}")
    private String ossSecretAccessKey;
    @Value("${baidu.oss.endpoint:}")
    private String ossEndpoint;
    @Value("${baidu.oss.bucket-name:}")
    private String ossBucketName;

    public BosClient getOssClient() {
        // 初始化一个BosClient
        BosClientConfiguration config = new BosClientConfiguration();
        config.setCredentials(new DefaultBceCredentials(ossAccessKeyId, ossSecretAccessKey));
        config.setEndpoint(ossEndpoint);
        return new BosClient(config);
    }

    public StsClient getStsClient() {
        // 创建一个StsClient实例
        BceCredentials credentials = new DefaultBceCredentials(ossAccessKeyId, ossSecretAccessKey);
        return new StsClient(
                new BceClientConfiguration().withEndpoint("http://sts.bj.baidubce.com").withCredentials(credentials)
        );
    }
}
