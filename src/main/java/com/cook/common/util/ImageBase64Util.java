package com.cook.common.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 图片Base64编码转换工具类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/7/8
 */
@Slf4j
public class ImageBase64Util {

    /**
     * Base64数据URL的正则表达式
     * 匹配格式：data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...
     */
    private static final Pattern DATA_URL_PATTERN = Pattern.compile("^data:image/([a-zA-Z]+);base64,(.+)$");

    /**
     * 支持的图片格式
     */
    private static final String[] SUPPORTED_FORMATS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

    /**
     * 将MultipartFile转换为Base64编码字符串
     *
     * @param file 图片文件
     * @return Base64编码字符串（不包含data:image前缀）
     * @throws IOException 文件读取异常
     */
    public static String multipartFileToBase64(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        validateImageFile(file);

        try (InputStream inputStream = file.getInputStream()) {
            byte[] bytes = IoUtil.readBytes(inputStream);
            return Base64.encode(bytes);
        }
    }

    /**
     * 将MultipartFile转换为Base64数据URL格式
     *
     * @param file 图片文件
     * @return Base64数据URL格式字符串（包含data:image前缀）
     * @throws IOException 文件读取异常
     */
    public static String multipartFileToDataUrl(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        validateImageFile(file);

        String contentType = file.getContentType();
        if (contentType == null) {
            contentType = "image/jpeg"; // 默认格式
        }

        String base64 = multipartFileToBase64(file);
        return String.format("data:%s;base64,%s", contentType, base64);
    }

    /**
     * 将Base64编码字符串转换为字节数组
     *
     * @param base64String Base64编码字符串（可以包含或不包含data:image前缀）
     * @return 图片字节数组
     */
    public static byte[] base64ToBytes(String base64String) {
        if (StrUtil.isBlank(base64String)) {
            throw new IllegalArgumentException("Base64字符串不能为空");
        }

        // 处理data URL格式
        String cleanBase64 = cleanBase64String(base64String);
        
        try {
            return Base64.decode(cleanBase64);
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的Base64编码字符串", e);
        }
    }

    /**
     * 将Base64编码字符串转换为InputStream
     *
     * @param base64String Base64编码字符串
     * @return 图片输入流
     */
    public static InputStream base64ToInputStream(String base64String) {
        byte[] bytes = base64ToBytes(base64String);
        return new ByteArrayInputStream(bytes);
    }

    /**
     * 将Base64编码字符串转换为临时文件
     *
     * @param base64String Base64编码字符串
     * @param fileExtension 文件扩展名（如：jpg、png）
     * @return 临时文件
     * @throws IOException 文件创建异常
     */
    public static File base64ToTempFile(String base64String, String fileExtension) throws IOException {
        if (StrUtil.isBlank(fileExtension)) {
            fileExtension = "jpg"; // 默认扩展名
        }

        // 确保扩展名不包含点号
        if (!fileExtension.startsWith(".")) {
            fileExtension = "." + fileExtension;
        }

        byte[] bytes = base64ToBytes(base64String);
        
        // 创建临时文件
        File tempFile = File.createTempFile("image_", fileExtension);
        
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(bytes);
        }
        
        log.debug("创建临时文件：{}，大小：{} bytes", tempFile.getAbsolutePath(), bytes.length);
        return tempFile;
    }

    /**
     * 将Base64编码字符串保存为文件
     *
     * @param base64String Base64编码字符串
     * @param filePath 目标文件路径
     * @throws IOException 文件保存异常
     */
    public static void base64ToFile(String base64String, String filePath) throws IOException {
        byte[] bytes = base64ToBytes(base64String);
        Path path = Paths.get(filePath);
        
        // 确保父目录存在
        Files.createDirectories(path.getParent());
        
        Files.write(path, bytes);
        log.info("Base64图片已保存到：{}，大小：{} bytes", filePath, bytes.length);
    }

    /**
     * 从文件路径读取图片并转换为Base64编码
     *
     * @param filePath 图片文件路径
     * @return Base64编码字符串
     * @throws IOException 文件读取异常
     */
    public static String fileToBase64(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            throw new FileNotFoundException("文件不存在：" + filePath);
        }

        byte[] bytes = Files.readAllBytes(path);
        return Base64.encode(bytes);
    }

    /**
     * 从文件读取图片并转换为Base64数据URL格式
     *
     * @param filePath 图片文件路径
     * @return Base64数据URL格式字符串
     * @throws IOException 文件读取异常
     */
    public static String fileToDataUrl(String filePath) throws IOException {
        String base64 = fileToBase64(filePath);
        String contentType = getContentTypeFromPath(filePath);
        return String.format("data:%s;base64,%s", contentType, base64);
    }

    /**
     * 验证Base64字符串是否为有效的图片
     *
     * @param base64String Base64编码字符串
     * @return 是否为有效图片
     */
    public static boolean isValidImageBase64(String base64String) {
        try {
            byte[] bytes = base64ToBytes(base64String);
            try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes)) {
                BufferedImage image = ImageIO.read(bis);
                return image != null;
            }
        } catch (Exception e) {
            log.debug("Base64字符串不是有效的图片格式", e);
            return false;
        }
    }

    /**
     * 从Base64字符串中提取图片格式
     *
     * @param base64String Base64编码字符串（支持data URL格式）
     * @return 图片格式（如：jpeg、png）
     */
    public static String extractImageFormat(String base64String) {
        if (StrUtil.isBlank(base64String)) {
            return null;
        }

        // 尝试从data URL中提取格式
        Matcher matcher = DATA_URL_PATTERN.matcher(base64String.trim());
        if (matcher.matches()) {
            return matcher.group(1);
        }

        // 如果不是data URL格式，尝试通过解码后的字节判断
        try {
            byte[] bytes = base64ToBytes(base64String);
            try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes)) {
                BufferedImage image = ImageIO.read(bis);
                if (image != null) {
                    // 通过文件头判断格式
                    return detectImageFormat(bytes);
                }
            }
        } catch (Exception e) {
            log.debug("无法提取图片格式", e);
        }

        return null;
    }

    /**
     * 获取Base64编码图片的尺寸信息
     *
     * @param base64String Base64编码字符串
     * @return 图片尺寸数组 [width, height]，如果无法获取则返回null
     */
    public static int[] getImageDimensions(String base64String) {
        try {
            byte[] bytes = base64ToBytes(base64String);
            try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes)) {
                BufferedImage image = ImageIO.read(bis);
                if (image != null) {
                    return new int[]{image.getWidth(), image.getHeight()};
                }
            }
        } catch (Exception e) {
            log.debug("无法获取图片尺寸", e);
        }
        return null;
    }

    /**
     * 清理Base64字符串，移除data URL前缀
     *
     * @param base64String 原始Base64字符串
     * @return 清理后的Base64字符串
     */
    private static String cleanBase64String(String base64String) {
        if (StrUtil.isBlank(base64String)) {
            return base64String;
        }

        String trimmed = base64String.trim();
        
        // 检查是否为data URL格式
        Matcher matcher = DATA_URL_PATTERN.matcher(trimmed);
        if (matcher.matches()) {
            return matcher.group(2); // 返回Base64部分
        }

        return trimmed;
    }

    /**
     * 验证文件是否为有效的图片文件
     *
     * @param file 文件
     */
    private static void validateImageFile(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IllegalArgumentException("文件不是图片格式：" + contentType);
        }

        // 验证文件大小（限制10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("图片文件过大，最大支持10MB");
        }
    }

    /**
     * 根据文件路径获取Content-Type
     *
     * @param filePath 文件路径
     * @return Content-Type
     */
    private static String getContentTypeFromPath(String filePath) {
        String extension = getFileExtension(filePath).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            default:
                return "image/jpeg"; // 默认格式
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 文件扩展名
     */
    private static String getFileExtension(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        return lastDotIndex > 0 ? filePath.substring(lastDotIndex + 1) : "";
    }

    /**
     * 通过文件头检测图片格式
     *
     * @param bytes 图片字节数组
     * @return 图片格式
     */
    private static String detectImageFormat(byte[] bytes) {
        if (bytes.length < 4) {
            return "jpeg"; // 默认格式
        }

        // JPEG: FF D8 FF
        if (bytes[0] == (byte) 0xFF && bytes[1] == (byte) 0xD8 && bytes[2] == (byte) 0xFF) {
            return "jpeg";
        }
        
        // PNG: 89 50 4E 47
        if (bytes[0] == (byte) 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
            return "png";
        }
        
        // GIF: 47 49 46 38
        if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x38) {
            return "gif";
        }
        
        // BMP: 42 4D
        if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
            return "bmp";
        }

        return "jpeg"; // 默认格式
    }
}
