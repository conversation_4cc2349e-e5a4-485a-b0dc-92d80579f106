package com.cook.common.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @description 用户上下文工具类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
public class UserContext {

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object userId = request.getAttribute("userId");
            if (userId instanceof Long) {
                return (Long) userId;
            }
        }
        return null;
    }

    /**
     * 获取当前用户openid
     */
    public static String getCurrentOpenid() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object openid = request.getAttribute("openid");
            if (openid instanceof String) {
                return (String) openid;
            }
        }
        return null;
    }

    /**
     * 获取当前用户昵称
     */
    public static String getCurrentNickname() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object nickname = request.getAttribute("nickname");
            if (nickname instanceof String) {
                return (String) nickname;
            }
        }
        return null;
    }

    /**
     * 获取当前用户token
     */
    public static String getCurrentToken() {
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            Object token = request.getAttribute("token");
            if (token instanceof String) {
                return (String) token;
            }
        }
        return null;
    }

    /**
     * 获取当前请求
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
