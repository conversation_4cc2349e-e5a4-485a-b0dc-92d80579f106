package com.cook.enums;

/**
 * 税种税项信息枚举
 */
public enum TaxAssessmentType {
    /**
     * 税种税项信息
     */
    SHOULE_TAX_AMOUNT_TYPE((byte)1, "应纳税额"),
    TAX_AMOUNT_DISCUNT_TYPE((byte)2, "减免税额"),
    PAYED_TAX_AMOUNT_TYPE((byte)3, "已缴税额"),
    SHOULD_TAX_AGENCY_AMOUNT_TYPE((byte)4, "应代征税额"),
    TAXED_AGENCY_AMOUNT_TYPE((byte)5, "已代征税额"),
    PLAN_TAX_BASIS_TYPE((byte)6, "计税依据"),
    SHOULD_TAX_AGENCY_RATE((byte)7, "增值税小规模纳税人减征额");

    private final Byte taxAssessmentTypeCode;
    private final String taxAssessmentTypeDesc;

    TaxAssessmentType(Byte taxAssessmentTypeCode, String taxAssessmentTypeDesc) {
        this.taxAssessmentTypeCode = taxAssessmentTypeCode;
        this.taxAssessmentTypeDesc = taxAssessmentTypeDesc;
    }

    public Byte getCode() {
        return taxAssessmentTypeCode;
    }

    public String getDesc() {
        return taxAssessmentTypeDesc;
    }
}
