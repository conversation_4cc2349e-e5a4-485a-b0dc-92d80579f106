package com.cook.enums;

/**
 * 税种税项类型枚举
 */
public enum TaxAssessmentTypeEnum {
    /**
     * 税种
     */
    SHOULE_TAX_AMOUNT(TaxAssessmentType.SHOULE_TAX_AMOUNT_TYPE, "taxAmount"),
    TAX_AMOUNT_DISCUNT(TaxAssessmentType.TAX_AMOUNT_DISCUNT_TYPE, "taxReduction"),
    PAYED_TAX_AMOUNT(TaxAssessmentType.PAYED_TAX_AMOUNT_TYPE, "paymentedTaxAmount"),
    SHOULD_TAX_AGENCY_AMOUNT(TaxAssessmentType.SHOULD_TAX_AGENCY_AMOUNT_TYPE, "shouldSubstitutedTax"),
    TAXED_AGENCY_AMOUNT(TaxAssessmentType.TAXED_AGENCY_AMOUNT_TYPE, "substitutedTax"),
    PLAN_TAX_BASIS(TaxAssessmentType.PLAN_TAX_BASIS_TYPE, "taxBasis"),
    VAT_SMALL_SCALE_AMOUNT(TaxAssessmentType.SHOULD_TAX_AGENCY_RATE, "vaTSmallScaleAmount");

    private final TaxAssessmentType taxAssessmentType;
    private final String taxAssessmentTypeCode;

    TaxAssessmentTypeEnum(TaxAssessmentType taxAssessmentType, String taxAssessmentTypeCode){
        this.taxAssessmentType = taxAssessmentType;
        this.taxAssessmentTypeCode = taxAssessmentTypeCode;
    }

    public TaxAssessmentType getTaxAssessmentType() {
        return taxAssessmentType;
    }

    public String getTaxAssessmentTypeCode() {
        return taxAssessmentTypeCode;
    }

    /**
     * 根据参数代码获取中文描述
     */
    public static String getChineseDesc(String paramCode) {
        for (TaxAssessmentTypeEnum typeEnum : values()) {
            if (typeEnum.getTaxAssessmentTypeCode().equals(paramCode)) {
                return typeEnum.getTaxAssessmentType().getDesc();
            }
        }
        return paramCode; // 如果找不到对应的中文，返回原参数
    }

    /**
     * 获取所有参数映射
     */
    public static java.util.Map<String, String> getAllParamMapping() {
        java.util.Map<String, String> mapping = new java.util.HashMap<>();
        for (TaxAssessmentTypeEnum typeEnum : values()) {
            mapping.put(typeEnum.getTaxAssessmentTypeCode(), typeEnum.getTaxAssessmentType().getDesc());
        }
        // 添加其他常用参数
        mapping.put("payAmount", "支付金额");
        mapping.put("taxRate", "税率");
        mapping.put("taxBaseRate", "计税基础税率");
        mapping.put("taxRaduceRate", "减征税率");
        return mapping;
    }
}
