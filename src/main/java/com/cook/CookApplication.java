package com.cook;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@Configuration
@EnableFeignClients
@MapperScan("com.cook.model.mapper")
@SpringBootApplication
public class CookApplication {
    
    
    

    public static void main(String[] args) {
        SpringApplication.run(CookApplication.class, args);
    }


    
}
