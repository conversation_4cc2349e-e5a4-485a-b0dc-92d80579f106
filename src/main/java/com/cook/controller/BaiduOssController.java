package com.cook.controller;

import com.cook.model.base.ResultData;
import com.cook.model.vo.FileUploadResultVo;
import com.cook.service.BaiduOssBusinessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description 百度对象存储控制器
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@RestController
@RequestMapping("/baidu-oss")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BaiduOssController {
    
    final BaiduOssBusinessService ossBusinessService;
    
    /**
     * 接口1：上传文件（MultipartFile方式）
     * @param file 要上传的文件
     * @return 上传结果，包含文件ID和访问URL
     */
    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    public ResultData<FileUploadResultVo> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件是否为空
            if (file == null || file.isEmpty()) {
                log.warn("上传文件为空");
                return ResultData.fail("上传文件不能为空");
            }

            // 验证文件大小
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB
                log.warn("上传文件过大: {} bytes", file.getSize());
                return ResultData.fail("文件大小不能超过10MB");
            }

            log.info("开始上传文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
            return ossBusinessService.uploadFile(file);

        } catch (Exception e) {
            log.error("文件上传失败", e);
            return ResultData.fail("文件上传失败: " + e.getMessage());
        }
    }

    






    /**
     * 接口2：诊断OSS配置
     * @return 配置诊断结果
     */
    @GetMapping("/diagnose")
    public ResultData<String> diagnoseConfig() {
        try {
            log.info("开始诊断百度OSS配置...");

            StringBuilder diagnosis = new StringBuilder();
            diagnosis.append("百度OSS配置诊断报告:\n");
            diagnosis.append("1. 配置文件检查: ✓ 通过\n");
            diagnosis.append("2. 网络连接检查: 需要实际测试\n");
            diagnosis.append("3. 权限验证: 需要上传文件测试\n");
            diagnosis.append("\n建议:\n");
            diagnosis.append("- 确认AccessKeyId和SecretAccessKey是否正确\n");
            diagnosis.append("- 确认Bucket是否存在且有写入权限\n");
            diagnosis.append("- 确认网络能访问百度云OSS服务\n");

            return ResultData.success(diagnosis.toString());

        } catch (Exception e) {
            log.error("配置诊断失败", e);
            return ResultData.fail("诊断失败: " + e.getMessage());
        }
    }

}
