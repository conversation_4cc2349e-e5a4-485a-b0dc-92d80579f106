package com.cook.controller;

import com.cook.model.base.ResultData;
import com.cook.model.dto.RecipeSearchDto;
import com.cook.model.vo.PageResult;
import com.cook.model.vo.RecipeDetailVo;
import com.cook.model.vo.RecipeListVo;
import com.cook.service.RecipeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 菜谱相关控制器
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@RestController
@RequestMapping("/recipe")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RecipeController {
    
    final RecipeService recipeService;
    
    /**
     * 搜索菜谱
     * 支持根据菜名模糊匹配或根据食材名称精确匹配
     * @param dto 搜索参数
     * @return 菜谱列表
     */
    @PostMapping(value = "/search", consumes = "application/json", produces = "application/json")
    public ResultData<PageResult<RecipeListVo>> searchRecipes(@RequestBody RecipeSearchDto dto) {
        return recipeService.searchRecipes(dto);
    }
    
    /**
     * 根据菜谱ID获取菜谱详情
     * 包含菜谱基本信息、食材列表、制作步骤
     * @param recipeId 菜谱ID
     * @return 菜谱详情
     */
    @GetMapping("/detail/{recipeId}")
    public ResultData<RecipeDetailVo> getRecipeDetail(@PathVariable Integer recipeId) {
        return recipeService.getRecipeDetail(recipeId);
    }
    
    /**
     * 根据食材名称快速搜索菜谱
     * @param ingredientNames 食材名称，多个用逗号分隔
     * @return 菜谱列表
     */
    @GetMapping("/search-by-ingredients")
    public ResultData<PageResult<RecipeListVo>> searchRecipesByIngredients(
            @RequestParam String ingredientNames,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        RecipeSearchDto dto = new RecipeSearchDto();
        dto.setIngredientNames(java.util.Arrays.asList(ingredientNames.split(",")));
        dto.setPageNum(pageNum);
        dto.setPageSize(pageSize);
        
        return recipeService.searchRecipes(dto);
    }
}
