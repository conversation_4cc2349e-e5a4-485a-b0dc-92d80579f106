package com.cook.controller;

import com.cook.model.base.ResultData;
import com.cook.model.dto.CookImageInfoDto;
import com.cook.model.dto.CookVersionRecordPageDto;
import com.cook.model.vo.CookAiVersionInfoVo;
import com.cook.model.vo.PageResult;
import com.cook.service.CookInfoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/14 11:35
 */
//@Api(tags = {"智能菜谱"})
@RestController
@RequestMapping("/cook")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CookInfoController {
    final CookInfoService cookInfoService;

    /**
     * 原有接口：支持Base64格式的多文件上传
     */
    @PostMapping(value = "/upload/photos", consumes = "application/json", produces = "application/json")
    public ResultData<List<CookAiVersionInfoVo>> uploadPhotos(@Valid @RequestBody CookImageInfoDto dto) {
        return cookInfoService.upload(dto);
    }

    /**
     * 新增接口：支持MultipartFile格式的多文件上传
     * @param files 上传的文件数组
     * @param description 可选的描述信息
     * @param enableAiRecognition 是否启用AI识别，默认true
     * @return 识别结果列表
     */
    @PostMapping(value = "/upload/photos/multipart", consumes = "multipart/form-data", produces = "application/json")
    public ResultData<List<CookAiVersionInfoVo>> uploadPhotosMultipart(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "enableAiRecognition", defaultValue = "true") Boolean enableAiRecognition) {

        return cookInfoService.uploadMultipart(files, description, enableAiRecognition);
    }

    /**
     * 分页查询用户的CookVersionRecord数据
     * @param dto 分页查询参数
     * @return 分页结果
     */
    @PostMapping(value = "/version-records/page", consumes = "application/json", produces = "application/json")
    public ResultData<PageResult<CookAiVersionInfoVo>> getVersionRecordsPage(@Valid @RequestBody CookVersionRecordPageDto dto) {
        return cookInfoService.getVersionRecordsPage(dto);
    }
}
