package com.cook.controller;

import cn.hutool.http.HttpUtil;
import com.cook.model.base.ResultData;
import com.cook.service.impl.LocalRecipeImportService;
import com.cook.service.impl.MarkdownRecipeParser;
import com.cook.service.impl.MarkdownRecipeParser.ParsedRecipe;
import com.cook.service.impl.RecipeParserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 菜谱解析控制器
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@RestController
@RequestMapping("/recipe-parser")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RecipeParserController {
    
    final MarkdownRecipeParser markdownRecipeParser;
    final RecipeParserService recipeParserService;
    final LocalRecipeImportService localRecipeImportService;
    
    /**
     * 解析GitHub上的Markdown菜谱
     * @param githubUrl GitHub文件URL
     * @return 解析后的菜谱信息
     */
    @PostMapping("/parse-github")
    public ResultData<ParsedRecipe> parseGithubRecipe(@RequestParam String githubUrl) {
        try {
            log.info("开始解析GitHub菜谱：{}", githubUrl);
            
            // 转换为raw URL
            String rawUrl = convertToRawUrl(githubUrl);
            log.info("转换后的raw URL：{}", rawUrl);
            
            // 获取Markdown内容
            String markdownContent = HttpUtil.get(rawUrl);
            if (markdownContent == null || markdownContent.trim().isEmpty()) {
                return ResultData.fail("无法获取菜谱内容");
            }
            
            // 解析菜谱
            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
            
            log.info("菜谱解析完成：{}", recipe.getName());
            return ResultData.success(recipe);
            
        } catch (Exception e) {
            log.error("解析GitHub菜谱失败", e);
            return ResultData.fail("解析失败：" + e.getMessage());
        }
    }
    
    /**
     * 直接解析Markdown内容
     * @param markdownContent Markdown内容
     * @return 解析后的菜谱信息
     */
    @PostMapping("/parse-markdown")
    public ResultData<ParsedRecipe> parseMarkdown(@RequestBody String markdownContent) {
        try {
            log.info("开始解析Markdown菜谱内容");
            
            if (markdownContent == null || markdownContent.trim().isEmpty()) {
                return ResultData.fail("Markdown内容不能为空");
            }
            
            // 解析菜谱
            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
            
            log.info("菜谱解析完成：{}", recipe.getName());
            return ResultData.success(recipe);
            
        } catch (Exception e) {
            log.error("解析Markdown菜谱失败", e);
            return ResultData.fail("解析失败：" + e.getMessage());
        }
    }
    
    /**
     * 解析本地菜谱文件并保存到数据库
     * @param categoryFolderName 分类文件夹名称
     * @param fileName 菜谱文件名
     * @return 保存结果，包含菜谱ID
     */
    @PostMapping("/parse-and-save-local")
    public ResultData<Integer> parseAndSaveLocalRecipe(@RequestParam String categoryFolderName,
                                                       @RequestParam String fileName) {
        try {
            log.info("开始解析并保存本地菜谱：{}/{}", categoryFolderName, fileName);

            // 解析本地菜谱文件
            ParsedRecipe recipe = localRecipeImportService.importSingleRecipe(categoryFolderName, fileName);

            // 保存到数据库
            Integer recipeId = recipeParserService.saveRecipeToDatabase(recipe);

            log.info("菜谱 {} 解析并保存完成，ID：{}", recipe.getName(), recipeId);
            return ResultData.success(recipeId);

        } catch (Exception e) {
            log.error("解析并保存本地菜谱失败", e);
            return ResultData.fail("解析并保存失败：" + e.getMessage());
        }
    }

    /**
     * 批量导入本地菜谱文件
     * @return 导入结果统计
     */
    @PostMapping("/batch-import-local")
    public ResultData<LocalRecipeImportService.ImportResult> batchImportLocalRecipes() {
        try {
            log.info("开始批量导入本地菜谱文件");

            LocalRecipeImportService.ImportResult result = localRecipeImportService.batchImportLocalRecipes();

            log.info("批量导入完成，成功：{}，失败：{}", result.getTotalSuccess(), result.getTotalFailed());
            return ResultData.success(result);

        } catch (Exception e) {
            log.error("批量导入本地菜谱失败", e);
            return ResultData.fail("批量导入失败：" + e.getMessage());
        }
    }

    /**
     * 解析白灼菜心示例
     * @return 解析后的菜谱信息
     */
    @GetMapping("/parse-example")
    public ResultData<ParsedRecipe> parseExample() {
        try {
            String githubUrl = "https://github.com/Anduin2017/HowToCook/blob/master/dishes/vegetable_dish/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83.md";
            return parseGithubRecipe(githubUrl);
        } catch (Exception e) {
            log.error("解析示例菜谱失败", e);
            return ResultData.fail("解析失败：" + e.getMessage());
        }
    }

    /**
     * 解析并保存白灼菜心示例到数据库
     * @return 保存结果
     */
//    @PostMapping("/save-example")
//    public ResultData<Integer> saveExample() {
//        try {
//            String githubUrl = "https://github.com/Anduin2017/HowToCook/blob/master/dishes/vegetable_dish/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83/%E7%99%BD%E7%81%BC%E8%8F%9C%E5%BF%83.md";
//            return parseAndSaveGithubRecipe(githubUrl);
//        } catch (Exception e) {
//            log.error("保存示例菜谱失败", e);
//            return ResultData.fail("保存失败：" + e.getMessage());
//        }
//    }

    /**
     * 直接解析并保存白灼菜心（使用内置内容，不依赖网络）
     * @return 保存结果
     */
    @PostMapping("/save-baizhuocaixin")
    public ResultData<Integer> saveBaiZhuoCaiXin() {
        try {
            log.info("开始解析并保存白灼菜心菜谱");

            String markdownContent = """
                    # 白灼菜心的做法

                    <!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->

                    ![白灼菜心](./白灼菜心.jpg)

                    > 没有拍照，上图是网图，不过做出来都差不多啦

                    白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。

                    总之吧，减肥或者是**快速解决绿叶菜的绝佳方式**。

                    预估烹饪难度：★★

                    ## 计算

                    如下的量两个人够吃：

                    - 新鲜菜心 250g
                    - 生抽 5g
                    - 蚝油 5g
                    - 盐 5g
                    - 糖 3g
                    - 食用油 10g
                    - 大蒜 4-5瓣
                    - 小米辣 1-2根
                    - 清水 100g

                    ## 操作

                    1. 菜心洗净，去除根部比较硬或老的地方。用刀刮菜心根茎部分，刮掉外面那层比较硬的，菜心内部更可口，但要注意根茎白灼时长，时间太长的话根茎不脆了
                    2. 大蒜切成蒜末，有洋葱顺便加了点洋葱
                    3. 调制灵魂料汁：生抽5g、蚝油5g，加3g糖和100g清水半碗成一碗汁儿
                    4. 一锅500ml清水加5g盐和10g食用油烧开
                    5. 将菜心根茎在沸水中烫1分钟，直到根茎颜色变成深绿。再将整个菜心放到锅中烫熟1分钟，捞起来码入盘中
                    6. 开另一小锅将兑好的料汁倒入，小火烧开，放入一半的蒜末，一点点姜丝和小米椒碎。先在锅底倒油，五成热后倒入蒜末、洋葱，稍稍爆香后再加入料汁，加入小米辣煮开
                    7. 料汁稍微收汁，煮沸后稍等十来秒，后直接浇在菜心上，不要特别多，但蒜末还是很给力的不要少蒜
                    """;

            // 解析菜谱
            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);

            // 保存到数据库
            Integer recipeId = recipeParserService.saveRecipeToDatabase(recipe);

            log.info("白灼菜心菜谱保存成功，ID：{}", recipeId);
            return ResultData.success(recipeId);

        } catch (Exception e) {
            log.error("保存白灼菜心菜谱失败", e);
            return ResultData.fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 测试描述提取功能
     * @return 解析结果，用于验证描述提取是否正确
     */
    @GetMapping("/test-description")
    public ResultData<ParsedRecipe> testDescriptionExtraction() {
        try {
            String markdownContent = """
                    # 白灼菜心的做法

                    <!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->

                    ![白灼菜心](./白灼菜心.jpg)

                    > 没有拍照，上图是网图，不过做出来都差不多啦

                    白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。

                    总之吧，减肥或者是**快速解决绿叶菜的绝佳方式**。

                    预估烹饪难度：★★

                    ## 必备原料和工具

                    - 新鲜菜心
                    """;

            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);

            log.info("测试描述提取结果：");
            log.info("菜名：{}", recipe.getName());
            log.info("描述：{}", recipe.getDescription());
            log.info("难度：{}", recipe.getDifficultyLevel());

            return ResultData.success(recipe);

        } catch (Exception e) {
            log.error("测试描述提取失败", e);
            return ResultData.fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试白灼菜心导入（带分类）
     * @return 导入结果
     */
    @PostMapping("/test-import-with-category")
    public ResultData<Integer> testImportWithCategory() {
        try {
            log.info("测试白灼菜心导入（带分类）");

            String markdownContent = """
                    # 白灼菜心的做法

                    <!-- 标题必须是 `菜名` + `的做法`。和文件名一致。 -->

                    ![白灼菜心](./白灼菜心.jpg)

                    > 没有拍照，上图是网图，不过做出来都差不多啦

                    白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。

                    总之吧，减肥或者是**快速解决绿叶菜的绝佳方式**。

                    预估烹饪难度：★★

                    ## 计算

                    如下的量两个人够吃：

                    - 新鲜菜心 250g
                    - 生抽 5g
                    - 蚝油 5g
                    - 盐 5g
                    - 糖 3g
                    - 食用油 10g
                    - 大蒜 4-5瓣
                    - 小米辣 1-2根
                    - 清水 100g

                    ## 操作

                    1. 菜心洗净，去除根部比较硬或老的地方
                    2. 大蒜切成蒜末，有洋葱顺便加了点洋葱
                    3. 调制灵魂料汁：生抽5g、蚝油5g，加3g糖和100g清水半碗成一碗汁儿
                    4. 一锅500ml清水加5g盐和10g食用油烧开
                    5. 将菜心根茎在沸水中烫1分钟，直到根茎颜色变成深绿。再将整个菜心放到锅中烫熟1分钟，捞起来码入盘中
                    6. 开另一小锅将兑好的料汁倒入，小火烧开，放入一半的蒜末，一点点姜丝和小米椒碎
                    7. 料汁稍微收汁，煮沸后稍等十来秒，后直接浇在菜心上
                    """;

            // 解析菜谱
            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);

            // 设置为蔬菜分类
            recipe.setCategory(2); // 2-蔬菜

            // 保存到数据库
            Integer recipeId = recipeParserService.saveRecipeToDatabase(recipe);

            log.info("白灼菜心（蔬菜分类）导入成功，ID：{}", recipeId);
            return ResultData.success(recipeId);

        } catch (Exception e) {
            log.error("测试导入失败", e);
            return ResultData.fail("测试导入失败：" + e.getMessage());
        }
    }

    /**
     * 测试无序号步骤解析
     * @return 解析结果
     */
    @GetMapping("/test-bullet-steps")
    public ResultData<ParsedRecipe> testBulletSteps() {
        try {
            String markdownContent = """
                    # 红烧肉的做法

                    ## 操作

                    - 将肉刮洗干净，入煮锅煮至六成熟（变色为白），捞出趁热用蜂蜜、醋涂抹肉皮。
                    - 炒锅内放入熟猪油，用旺火烧至八成熟（约 200 度，油表有大量青烟，油状平静），将肉块皮朝下投入，炸至呈金红色时，捞入凉肉煮锅（之前煮完的煮锅）中泡软，放在案板上，切成三寸(10 cm)长、两分(0.6 cm)厚的片，仍然皮朝下，整齐装入蒸碗内。
                    - 将 5 克大葱切成 2.4 cm 长的段，5 克切成 2.4 cm 长的斜形片。姜去皮洗净，1.5 克切成片，5 克切成末，摊的鸡蛋皮切成 2.4 cm 长的等腰三角形片。
                    - 商芝入沸水锅中煮软捞出，去除老茎、杂质，淘洗干净，切成 3 cm 长的段，放入碗中,加酱油（5 克）、精盐（1 克）、熟猪油（10 克）拌匀，盖在肉片上，另将鸡汤（100 克）放入一小碗中，加酱油（5 克）、精盐（0.5 克）、料酒（15 克）搅匀，浇入蒸碗，再放入姜片、葱段、八角上笼用旺火蒸约半小时后，转用小火继续蒸约一小时三十分钟，熟烂后取出，拣去姜、葱、八角，倒、过滤原汁，将肉扣入汤盘。
                    - 炒锅内，放入鸡汤（100 克），加入原汁，用旺火烧沸，下入姜末、葱片、味精后搅匀，投入摊鸡蛋皮，淋芝麻油，浇入汤盘即成。
                    """;

            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);

            log.info("无序号步骤解析测试结果：");
            log.info("菜名：{}", recipe.getName());
            log.info("步骤数量：{}", recipe.getSteps().size());

            for (int i = 0; i < recipe.getSteps().size(); i++) {
                log.info("步骤 {}: {}", i + 1, recipe.getSteps().get(i).getDescription());
            }

            return ResultData.success(recipe);

        } catch (Exception e) {
            log.error("测试无序号步骤解析失败", e);
            return ResultData.fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试食材解析优化
     * @return 解析结果
     */
    @GetMapping("/test-ingredient-parsing")
    public ResultData<ParsedRecipe> testIngredientParsing() {
        try {
            String markdownContent = """
                    # 食材解析测试的做法

                    ## 计算

                    - 干辣椒 5g
                    - 干辣椒（或者二荆条） 10g
                    - 大蒜 1 个（约 20g）
                    - 新鲜菜心 250g
                    - 生抽、蚝油、盐 适量
                    - 老姜 一块
                    - 小葱/香葱 2根
                    - 熟猪油或菜籽油 15ml

                    ## 必备原料和工具

                    - 干辣椒（或者二荆条、小米椒）
                    - 大蒜、生姜、小葱
                    - 生抽、老抽、蚝油
                    """;

            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);

            log.info("食材解析优化测试结果：");
            log.info("菜名：{}", recipe.getName());
            log.info("食材数量：{}", recipe.getIngredients().size());

            for (var ingredient : recipe.getIngredients()) {
                log.info("食材：{} - 数量：{} - 类型：{}",
                        ingredient.getName(),
                        ingredient.getQuantity(),
                        ingredient.getType());
            }

            return ResultData.success(recipe);

        } catch (Exception e) {
            log.error("测试食材解析优化失败", e);
            return ResultData.fail("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 将GitHub URL转换为raw URL
     */
    private String convertToRawUrl(String githubUrl) {
        if (githubUrl.contains("raw.githubusercontent.com")) {
            return githubUrl;
        }
        
        // 将github.com/user/repo/blob/branch/path转换为raw.githubusercontent.com/user/repo/branch/path
        String rawUrl = githubUrl
                .replace("github.com", "raw.githubusercontent.com")
                .replace("/blob/", "/");
        
        // 移除查询参数
        if (rawUrl.contains("?")) {
            rawUrl = rawUrl.substring(0, rawUrl.indexOf("?"));
        }
        
        return rawUrl;
    }
}
