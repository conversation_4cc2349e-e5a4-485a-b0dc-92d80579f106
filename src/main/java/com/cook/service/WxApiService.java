package com.cook.service;

import com.cook.model.dto.WxSessionResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @description 微信API服务类，使用RestTemplate作为Feign的备用方案
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/7/12
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WxApiService {
    
    final RestTemplate restTemplate;
    
    /**
     * 调用微信code2session接口
     * @param appid 小程序appid
     * @param secret 小程序secret
     * @param jsCode 登录凭证
     * @param grantType 授权类型
     * @return 微信响应结果
     */
    public WxSessionResult code2Session(String appid, String secret, String jsCode, String grantType) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={jsCode}&grant_type={grantType}";
        
        log.info("调用微信code2session接口，appid: {}, jsCode: {}", appid, jsCode);
        
        try {
            WxSessionResult result = restTemplate.getForObject(url, WxSessionResult.class, appid, secret, jsCode, grantType);
            log.info("微信API响应: {}", result);
            return result;
        } catch (Exception e) {
            log.error("调用微信API失败", e);
            throw e;
        }
    }
}
