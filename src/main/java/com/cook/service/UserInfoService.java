package com.cook.service;

import com.cook.model.base.ResultData;
import com.cook.model.dto.WxLoginDto;
import com.cook.model.vo.LoginResultVo;
import com.cook.model.vo.UserInfoVo;

/**
 * <AUTHOR>
 * @description 用户信息服务接口
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
public interface UserInfoService {
    
    /**
     * 微信小程序登录
     * @param dto 登录参数
     * @return 登录结果
     */
    ResultData<LoginResultVo> wxLogin(WxLoginDto dto);
    
    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    ResultData<UserInfoVo> getUserInfo(Long userId);
    
    /**
     * 刷新用户token
     * @param userId 用户ID
     * @return 新的登录结果
     */
    ResultData<LoginResultVo> refreshToken(Long userId);
}
