package com.cook.service;

import com.cook.model.base.ResultData;
import com.cook.model.vo.FileUploadResultVo;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BaiduOssBusinessService {
    ResultData<FileUploadResultVo> uploadFile(@RequestParam("file") MultipartFile file);

    /**
     * 批量上传文件
     * @param files 要上传的文件数组
     * @return 上传结果列表
     */
    ResultData<List<FileUploadResultVo>> uploadFiles(MultipartFile[] files);
}
