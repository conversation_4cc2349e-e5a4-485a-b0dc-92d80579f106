package com.cook.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cook.common.util.JwtUtil;
import com.cook.model.base.ResultData;
import com.cook.model.dto.WxLoginDto;
import com.cook.model.dto.WxSessionResult;
import com.cook.model.entity.UserInfo;
import com.cook.model.mapper.UserInfoRepository;
import com.cook.model.vo.LoginResultVo;
import com.cook.model.vo.UserInfoVo;
import com.cook.service.UserInfoService;
import com.cook.service.client.WxServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 用户信息服务实现类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class UserInfoServiceImpl implements UserInfoService {
    
    final WxServiceClient wxServiceClient;
    final UserInfoRepository userInfoRepository;
    final JwtUtil jwtUtil;
    
    @Value("${wx.miniapp.appid:}")
    private String wxAppid;
    
    @Value("${wx.miniapp.secret:}")
    private String wxSecret;
    
    @Override
    public ResultData<LoginResultVo> wxLogin(WxLoginDto dto) {
        log.info("微信小程序登录，参数：{}", JSONUtil.toJsonStr(dto));
        
        try {
            // 1. 调用微信接口获取openid和session_key
            WxSessionResult sessionResult = wxServiceClient.code2Session(
                    wxAppid, wxSecret, dto.getCode(), "authorization_code"
            );
            
            log.info("微信code2session结果：{}", JSONUtil.toJsonStr(sessionResult));
            
            // 2. 检查微信接口调用是否成功
            if (sessionResult.getErrcode() != null && sessionResult.getErrcode() != 0) {
                return ResultData.fail("微信登录失败：" + sessionResult.getErrmsg());
            }
            
            if (StrUtil.isBlank(sessionResult.getOpenid())) {
                return ResultData.fail("获取用户openid失败");
            }
            
            // 3. 查询或创建用户
            UserInfo userInfo = getOrCreateUser(sessionResult, dto.getUserInfo());
            
            // 4. 生成JWT token
            String token = jwtUtil.generateToken(userInfo.getId(), userInfo.getOpenid(), userInfo.getNickname());
            Long expireTime = System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L; // 7天过期
            
            // 5. 构建返回结果
            UserInfoVo userInfoVo = new UserInfoVo();
            BeanUtils.copyProperties(userInfo, userInfoVo);
            
            LoginResultVo loginResult = LoginResultVo.builder()
                    .userId(userInfo.getId())
                    .token(token)
                    .expireTime(expireTime)
                    .userInfo(userInfoVo)
                    .build();
            
            return ResultData.success(loginResult);
            
        } catch (Exception e) {
            log.error("微信登录异常", e);
            return ResultData.fail("登录失败，请稍后重试");
        }
    }
    
    @Override
    public ResultData<UserInfoVo> getUserInfo(Long userId) {
        UserInfo userInfo = userInfoRepository.getById(userId);
        if (userInfo == null) {
            return ResultData.fail("用户不存在");
        }
        
        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userInfo, userInfoVo);
        
        return ResultData.success(userInfoVo);
    }
    
    @Override
    public ResultData<LoginResultVo> refreshToken(Long userId) {
        UserInfo userInfo = userInfoRepository.getById(userId);
        if (userInfo == null) {
            return ResultData.fail("用户不存在");
        }
        
        // 生成新JWT token
        String token = jwtUtil.generateToken(userInfo.getId(), userInfo.getOpenid(), userInfo.getNickname());
        Long expireTime = System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L;
        
        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(userInfo, userInfoVo);
        
        LoginResultVo loginResult = LoginResultVo.builder()
                .userId(userId)
                .token(token)
                .expireTime(expireTime)
                .userInfo(userInfoVo)
                .build();
        
        return ResultData.success(loginResult);
    }
    
    /**
     * 获取或创建用户
     */
    private UserInfo getOrCreateUser(WxSessionResult sessionResult, WxLoginDto.UserInfoDto userInfoDto) {
        // 根据openid查询用户
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getOpenid, sessionResult.getOpenid());
        UserInfo existUser = userInfoRepository.getOne(queryWrapper);
        
        Date now = new Date();
        
        if (existUser != null) {
            // 用户已存在，更新最后登录时间和用户信息
            existUser.setLastLoginTime(now);
            existUser.setUpdateTime(now);
            
            // 如果传入了用户信息，则更新
            if (userInfoDto != null) {
                updateUserInfo(existUser, userInfoDto);
            }
            
            userInfoRepository.updateById(existUser);
            return existUser;
        } else {
            // 创建新用户
            UserInfo newUser = UserInfo.builder()
                    .openid(sessionResult.getOpenid())
                    .unionid(sessionResult.getUnionid())
                    .status(0) // 正常状态
                    .lastLoginTime(now)
                    .createTime(now)
                    .updateTime(now)
                    .build();
            
            // 如果传入了用户信息，则设置
            if (userInfoDto != null) {
                updateUserInfo(newUser, userInfoDto);
            }
            
            userInfoRepository.save(newUser);
            return newUser;
        }
    }
    
    /**
     * 更新用户信息
     */
    private void updateUserInfo(UserInfo userInfo, WxLoginDto.UserInfoDto userInfoDto) {
        if (StrUtil.isNotBlank(userInfoDto.getNickName())) {
            userInfo.setNickname(userInfoDto.getNickName());
        }
        if (StrUtil.isNotBlank(userInfoDto.getAvatarUrl())) {
            userInfo.setAvatarUrl(userInfoDto.getAvatarUrl());
        }
        if (userInfoDto.getGender() != null) {
            userInfo.setGender(userInfoDto.getGender());
        }
        if (StrUtil.isNotBlank(userInfoDto.getCountry())) {
            userInfo.setCountry(userInfoDto.getCountry());
        }
        if (StrUtil.isNotBlank(userInfoDto.getProvince())) {
            userInfo.setProvince(userInfoDto.getProvince());
        }
        if (StrUtil.isNotBlank(userInfoDto.getCity())) {
            userInfo.setCity(userInfoDto.getCity());
        }
        if (StrUtil.isNotBlank(userInfoDto.getLanguage())) {
            userInfo.setLanguage(userInfoDto.getLanguage());
        }
    }

}
