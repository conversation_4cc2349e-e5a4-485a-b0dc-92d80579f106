package com.cook.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description Markdown菜谱解析器
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Component
public class MarkdownRecipeParser {
    
    /**
     * 解析Markdown格式的菜谱
     * @param markdownContent Markdown内容
     * @return 解析后的菜谱信息
     */
    public ParsedRecipe parseRecipe(String markdownContent) {
        ParsedRecipe recipe = new ParsedRecipe();
        
        // 解析标题
        recipe.setName(extractTitle(markdownContent));
        
        // 解析难度等级
        recipe.setDifficultyLevel(extractDifficulty(markdownContent));
        
        // 解析描述
        recipe.setDescription(extractDescription(markdownContent));
        
        // 解析食材
        recipe.setIngredients(extractIngredients(markdownContent));
        
        // 解析步骤
        recipe.setSteps(extractSteps(markdownContent));
        
        // 解析份数
        recipe.setServings(extractServings(markdownContent));
        
        return recipe;
    }
    
    /**
     * 提取标题
     */
    private String extractTitle(String content) {
        Pattern pattern = Pattern.compile("^#\\s+(.+?)的做法", Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // 备用模式
        pattern = Pattern.compile("^#\\s+(.+)", Pattern.MULTILINE);
        matcher = pattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).replace("的做法", "");
        }
        
        return "未知菜谱";
    }
    
    /**
     * 提取难度等级
     */
    private Integer extractDifficulty(String content) {
        Pattern pattern = Pattern.compile("预估烹饪难度：([★]+)");
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).length();
        }
        return 1; // 默认简单
    }
    
    /**
     * 提取描述
     */
    private String extractDescription(String content) {
        // 先清理HTML注释
        content = removeHtmlComments(content);

        // 提取第一段描述性文字
        String[] lines = content.split("\n");
        StringBuilder description = new StringBuilder();
        boolean foundTitle = false;

        for (String line : lines) {
            line = line.trim();

            // 跳过标题行
            if (line.startsWith("#")) {
                foundTitle = true;
                continue;
            }

            // 只有在找到标题后才开始收集描述
            if (foundTitle && StrUtil.isNotBlank(line)) {
                // 跳过需要过滤的内容
                if (shouldSkipLine(line)) {
                    continue;
                }

                // 遇到下一个标题停止
                if (line.startsWith("##")) {
                    break;
                }

                // 清理行内容并添加到描述中
                String cleanedLine = cleanDescriptionLine(line);
                if (StrUtil.isNotBlank(cleanedLine)) {
                    description.append(cleanedLine).append(" ");
                    if (description.length() > 300) break; // 限制长度
                }
            }
        }

        return description.toString().trim();
    }

    /**
     * 移除HTML注释
     */
    private String removeHtmlComments(String content) {
        // 移除HTML注释 <!-- ... -->
        Pattern commentPattern = Pattern.compile("<!--[\\s\\S]*?-->", Pattern.MULTILINE);
        return commentPattern.matcher(content).replaceAll("");
    }

    /**
     * 判断是否应该跳过这一行
     */
    private boolean shouldSkipLine(String line) {
        // 跳过图片链接
        if (line.startsWith("![") || line.matches(".*!\\[.*\\]\\(.*\\).*")) {
            return true;
        }

        // 跳过引用块
        if (line.startsWith(">")) {
            return true;
        }

        // 跳过空行
        if (line.isEmpty()) {
            return true;
        }

        // 跳过只包含特殊字符的行
        if (line.matches("^[\\s\\-=*_]+$")) {
            return true;
        }

        return false;
    }

    /**
     * 清理描述行内容
     */
    private String cleanDescriptionLine(String line) {
        // 移除Markdown格式标记
        line = line.replaceAll("\\*\\*(.*?)\\*\\*", "$1"); // 粗体
        line = line.replaceAll("\\*(.*?)\\*", "$1"); // 斜体
        line = line.replaceAll("`(.*?)`", "$1"); // 代码
        line = line.replaceAll("\\[(.*?)\\]\\(.*?\\)", "$1"); // 链接

        // 移除行内图片
        line = line.replaceAll("!\\[.*?\\]\\(.*?\\)", "");

        // 移除多余的空格
        line = line.replaceAll("\\s+", " ");

        return line.trim();
    }
    
    /**
     * 提取食材列表
     */
    private List<ParsedIngredient> extractIngredients(String content) {
        List<ParsedIngredient> ingredients = new ArrayList<>();
        
        // 查找计算部分的食材
        Pattern sectionPattern = Pattern.compile("##\\s+计算[\\s\\S]*?(?=##|$)");
        Matcher sectionMatcher = sectionPattern.matcher(content);
        
        if (sectionMatcher.find()) {
            String calculationSection = sectionMatcher.group();
            
            // 解析食材行
            Pattern ingredientPattern = Pattern.compile("-\\s+(.+?)\\s+(\\d+[gmkl克毫升个瓣根颗-]+)");
            Matcher ingredientMatcher = ingredientPattern.matcher(calculationSection);
            
            while (ingredientMatcher.find()) {
                String rawName = ingredientMatcher.group(1).trim();
                String quantity = ingredientMatcher.group(2).trim();

                // 标准化食材名称
                String standardizedName = standardizeIngredientName(rawName);

                ParsedIngredient ingredient = new ParsedIngredient();
                ingredient.setName(standardizedName);
                ingredient.setQuantity(quantity);
                ingredient.setType(determineIngredientType(standardizedName));

                ingredients.add(ingredient);
            }
        }
        
        // 如果没找到，尝试从必备原料部分提取
        if (ingredients.isEmpty()) {
            ingredients = extractIngredientsFromRequirements(content);
        }
        
        return ingredients;
    }
    
    /**
     * 从必备原料部分提取食材
     */
    private List<ParsedIngredient> extractIngredientsFromRequirements(String content) {
        List<ParsedIngredient> ingredients = new ArrayList<>();
        
        Pattern sectionPattern = Pattern.compile("##\\s+必备原料和工具[\\s\\S]*?(?=##|$)");
        Matcher sectionMatcher = sectionPattern.matcher(content);
        
        if (sectionMatcher.find()) {
            String section = sectionMatcher.group();
            String[] lines = section.split("\n");
            
            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("-") && !line.contains("工具")) {
                    String rawIngredientText = line.substring(1).trim();

                    // 处理可能包含多个食材的行，如"生抽、蚝油、盐"
                    String[] ingredientNames = splitMultipleIngredients(rawIngredientText);

                    for (String rawName : ingredientNames) {
                        String standardizedName = standardizeIngredientName(rawName);
                        if (StrUtil.isNotBlank(standardizedName)) {
                            ParsedIngredient ingredient = new ParsedIngredient();
                            ingredient.setName(standardizedName);
                            ingredient.setQuantity("适量");
                            ingredient.setType(determineIngredientType(standardizedName));

                            ingredients.add(ingredient);
                        }
                    }
                }
            }
        }
        
        return ingredients;
    }
    
    /**
     * 分割包含多个食材的字符串
     * 例如："生抽、蚝油、盐" -> ["生抽", "蚝油", "盐"]
     */
    private String[] splitMultipleIngredients(String ingredientText) {
        if (StrUtil.isBlank(ingredientText)) {
            return new String[0];
        }

        // 按照常见的分隔符分割
        String[] separators = {"、", "，", ",", "和", "及", "以及"};

        for (String separator : separators) {
            if (ingredientText.contains(separator)) {
                return ingredientText.split(separator);
            }
        }

        // 如果没有分隔符，返回原字符串
        return new String[]{ingredientText};
    }

    /**
     * 标准化食材名称
     * 去除空格后的描述和特殊字符后的内容
     */
    private String standardizeIngredientName(String rawName) {
        if (StrUtil.isBlank(rawName)) {
            return rawName;
        }

        String name = rawName.trim();

        // 去除空格后的所有内容
        // 例如：大蒜 1 个（约 -> 大蒜
        int spaceIndex = name.indexOf(' ');
        if (spaceIndex > 0) {
            name = name.substring(0, spaceIndex);
        }

        // 去除特殊字符及其后面的内容
        String[] specialChars = {"（", "(", "，", ",", "、", "或", "/"};
        for (String specialChar : specialChars) {
            int index = name.indexOf(specialChar);
            if (index > 0) {
                name = name.substring(0, index);
                break; // 找到第一个特殊字符就停止
            }
        }

        // 去除常见的修饰词
        String[] modifiers = {"新鲜", "干", "生", "熟", "老", "嫩", "大", "小", "中等"};
        for (String modifier : modifiers) {
            if (name.startsWith(modifier) && name.length() > modifier.length()) {
                name = name.substring(modifier.length());
                break;
            }
        }

        return name.trim();
    }

    /**
     * 判断食材类型
     */
    private String determineIngredientType(String name) {
        if (name.contains("油") || name.contains("盐") || name.contains("糖") ||
            name.contains("酱") || name.contains("醋") || name.contains("料酒") ||
            name.contains("生抽") || name.contains("蚝油") || name.contains("辣") ||
            name.contains("蒜") || name.contains("姜") || name.contains("葱")) {
            return "seasoning";
        } else if (name.contains("水") || name.contains("汤")) {
            return "supplement";
        } else {
            return "main";
        }
    }
    
    /**
     * 提取制作步骤
     */
    private List<ParsedStep> extractSteps(String content) {
        List<ParsedStep> steps = new ArrayList<>();

        Pattern sectionPattern = Pattern.compile("##\\s+操作[\\s\\S]*?(?=##|$)");
        Matcher sectionMatcher = sectionPattern.matcher(content);

        if (sectionMatcher.find()) {
            String operationSection = sectionMatcher.group();

            // 先尝试匹配有序号的步骤
            Pattern numberedStepPattern = Pattern.compile("(\\d+)\\. (.+?)(?=\\d+\\.|$)", Pattern.DOTALL);
            Matcher numberedMatcher = numberedStepPattern.matcher(operationSection);

            boolean hasNumberedSteps = false;
            while (numberedMatcher.find()) {
                hasNumberedSteps = true;
                String stepText = numberedMatcher.group(2).trim();

                ParsedStep step = new ParsedStep();
                step.setDescription(cleanStepText(stepText));
                step.setDurationSeconds(extractDuration(stepText));

                steps.add(step);
            }

            // 如果没有找到有序号的步骤，尝试匹配无序号的步骤（以 - 开头）
            if (!hasNumberedSteps) {
                steps = extractBulletPointSteps(operationSection);
            }
        }

        return steps;
    }

    /**
     * 提取无序号步骤（以 - 开头的列表项）
     */
    private List<ParsedStep> extractBulletPointSteps(String operationSection) {
        List<ParsedStep> steps = new ArrayList<>();

        // 按行分割
        String[] lines = operationSection.split("\n");
        StringBuilder currentStep = new StringBuilder();

        for (String line : lines) {
            line = line.trim();

            // 跳过标题行和空行
            if (line.startsWith("##") || line.isEmpty()) {
                continue;
            }

            // 如果是新的步骤（以 - 开头）
            if (line.startsWith("- ")) {
                // 保存前一个步骤
                if (currentStep.length() > 0) {
                    String stepText = currentStep.toString().trim();
                    ParsedStep step = new ParsedStep();
                    step.setDescription(cleanStepText(stepText));
                    step.setDurationSeconds(extractDuration(stepText));
                    steps.add(step);
                    currentStep = new StringBuilder();
                }

                // 开始新步骤，去掉开头的 "- "
                currentStep.append(line.substring(2).trim());
            } else if (currentStep.length() > 0) {
                // 继续当前步骤的内容
                currentStep.append(" ").append(line);
            }
        }

        // 保存最后一个步骤
        if (currentStep.length() > 0) {
            String stepText = currentStep.toString().trim();
            ParsedStep step = new ParsedStep();
            step.setDescription(cleanStepText(stepText));
            step.setDurationSeconds(extractDuration(stepText));
            steps.add(step);
        }

        return steps;
    }

    /**
     * 清理步骤文本
     */
    private String cleanStepText(String stepText) {
        if (stepText == null) {
            return "";
        }

        // 移除多余的空白字符
        stepText = stepText.replaceAll("\\s+", " ");

        // 移除可能的Markdown格式
        stepText = stepText.replaceAll("\\*\\*(.*?)\\*\\*", "$1"); // 粗体
        stepText = stepText.replaceAll("\\*(.*?)\\*", "$1"); // 斜体
        stepText = stepText.replaceAll("`(.*?)`", "$1"); // 代码

        return stepText.trim();
    }
    
    /**
     * 提取时间信息
     */
    private Integer extractDuration(String text) {
        // 匹配分钟
        Pattern minutePattern = Pattern.compile("(\\d+)\\s*分钟");
        Matcher minuteMatcher = minutePattern.matcher(text);
        if (minuteMatcher.find()) {
            return Integer.parseInt(minuteMatcher.group(1)) * 60;
        }
        
        // 匹配秒
        Pattern secondPattern = Pattern.compile("(\\d+)\\s*秒");
        Matcher secondMatcher = secondPattern.matcher(text);
        if (secondMatcher.find()) {
            return Integer.parseInt(secondMatcher.group(1));
        }
        
        return null;
    }
    
    /**
     * 提取份数
     */
    private Integer extractServings(String content) {
        Pattern pattern = Pattern.compile("([一二三四五六七八九十\\d]+)\\s*[个人]");
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            String servingText = matcher.group(1);
            return convertChineseNumber(servingText);
        }
        return 1; // 默认1人份
    }
    
    /**
     * 转换中文数字
     */
    private Integer convertChineseNumber(String chineseNum) {
        switch (chineseNum) {
            case "一": return 1;
            case "二": return 2;
            case "三": return 3;
            case "四": return 4;
            case "五": return 5;
            case "六": return 6;
            case "七": return 7;
            case "八": return 8;
            case "九": return 9;
            case "十": return 10;
            default:
                try {
                    return Integer.parseInt(chineseNum);
                } catch (NumberFormatException e) {
                    return 1;
                }
        }
    }
    
    /**
     * 解析后的菜谱信息
     */
    @Data
    public static class ParsedRecipe {
        private String name;
        private String description;
        private Integer difficultyLevel;
        private Integer servings;
        private Integer category;
        private List<ParsedIngredient> ingredients;
        private List<ParsedStep> steps;
    }
    
    /**
     * 解析后的食材信息
     */
    @Data
    public static class ParsedIngredient {
        private String name;
        private String quantity;
        private String type; // main, supplement, seasoning
    }
    
    /**
     * 解析后的步骤信息
     */
    @Data
    public static class ParsedStep {
        private String description;
        private Integer durationSeconds;
    }
}
