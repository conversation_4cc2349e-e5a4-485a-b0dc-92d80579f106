package com.cook.service.impl;

import com.cook.model.base.ResultData;
import com.cook.model.vo.FileUploadResultVo;
import com.cook.service.BaiduOssBusinessService;
import com.cook.service.client.BaiduOssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BaiduOssBusinessServiceImpl implements BaiduOssBusinessService {
    private final BaiduOssService baiduOssService;

    @Override
    public ResultData<FileUploadResultVo> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            log.info("开始上传文件：{}", file.getOriginalFilename());

            // 验证文件
            if (file == null || file.isEmpty()) {
                return ResultData.fail("文件不能为空");
            }

            // 验证文件大小（限制10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return ResultData.fail("文件大小不能超过10MB");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            log.info("文件类型：{}", contentType);
            if (!isValidFileType(contentType)) {
                return ResultData.fail("不支持的文件类型：" + contentType);
            }

            // 转换为临时文件
            File tempFile = convertToFile(file);

            try {
                // 上传文件
                String fileId = baiduOssService.uploadFile(tempFile, getFileSuffix(Objects.requireNonNull(file.getOriginalFilename())));

                // 生成访问URL
                String fileUrl = baiduOssService.generateUrl( fileId);

                FileUploadResultVo result = new FileUploadResultVo();
                result.setFileId(fileId);
                result.setFileName(file.getOriginalFilename());
                result.setFileUrl(fileUrl);
                result.setFileSize(file.getSize());
                result.setContentType(contentType);

                log.info("文件上传成功，ID：{}，URL：{}", fileId, fileUrl);
                return ResultData.success(result);

            } finally {
                // 清理临时文件
                if (tempFile != null && tempFile.exists()) {
                    boolean deleted = tempFile.delete();
                    if (!deleted) {
                        log.warn("临时文件删除失败：{}", tempFile.getAbsolutePath());
                    }
                }
            }

        } catch (Exception e) {
            log.error("文件上传失败", e);
            return ResultData.fail("上传失败：" + e.getMessage());
        }
    }

    @Override
    public ResultData<List<FileUploadResultVo>> uploadFiles(MultipartFile[] files) {
        try {
            log.info("开始批量上传文件，文件数量：{}", files.length);

            // 验证文件数组
            if (files == null || files.length == 0) {
                return ResultData.fail("文件列表不能为空");
            }

            // 限制批量上传文件数量
            if (files.length > 10) {
                return ResultData.fail("单次最多只能上传10个文件");
            }

            List<FileUploadResultVo> results = new ArrayList<>();
            List<String> failedFiles = new ArrayList<>();

            // 遍历上传每个文件
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                try {
                    log.info("正在上传第{}个文件：{}", i + 1, file.getOriginalFilename());

                    // 调用单文件上传方法
                    ResultData<FileUploadResultVo> uploadResult = uploadFile(file);

                    if (uploadResult.isSuccess()) {
                        results.add(uploadResult.getData());
                        log.info("第{}个文件上传成功：{}", i + 1, file.getOriginalFilename());
                    } else {
                        failedFiles.add(file.getOriginalFilename() + ": " + uploadResult.getMsg());
                        log.warn("第{}个文件上传失败：{}，原因：{}", i + 1, file.getOriginalFilename(), uploadResult.getMsg());
                    }

                } catch (Exception e) {
                    failedFiles.add(file.getOriginalFilename() + ": " + e.getMessage());
                    log.error("第{}个文件上传异常：{}", i + 1, file.getOriginalFilename(), e);
                }
            }

            // 构建返回结果
            if (results.isEmpty()) {
                return ResultData.fail("所有文件上传失败：" + String.join("; ", failedFiles));
            } else if (!failedFiles.isEmpty()) {
                log.warn("部分文件上传失败：{}", String.join("; ", failedFiles));
                // 部分成功的情况，返回成功但在消息中提示失败的文件
                return ResultData.success("部分文件上传成功，失败文件：" + String.join("; ", failedFiles), results);
            } else {
                log.info("所有文件上传成功，共{}个文件", results.size());
                return ResultData.success("所有文件上传成功", results);
            }

        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return ResultData.fail("批量上传失败：" + e.getMessage());
        }
    }

    /**
     * 验证文件类型
     */
    private boolean isValidFileType(String contentType) {
        if (contentType == null) {
            return false;
        }

        // 支持的文件类型
        return contentType.startsWith("image/");
    }

    /**
     * 将MultipartFile转换为File
     */
    private File convertToFile(MultipartFile multipartFile) throws IOException {
        String originalFilename = multipartFile.getOriginalFilename();
        String suffix = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        File tempFile = File.createTempFile("upload_", suffix);
        multipartFile.transferTo(tempFile);
        return tempFile;
    }

    private static String getFileSuffix(String fileName) {

        return fileName.substring(fileName.lastIndexOf(".")+1);
    }

}
