package com.cook.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.cook.common.enums.RecipeCategoryEnum;
import com.cook.service.impl.MarkdownRecipeParser.ParsedRecipe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 本地菜谱导入服务
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class LocalRecipeImportService {
    
    final MarkdownRecipeParser markdownRecipeParser;
    final RecipeParserService recipeParserService;
    
    @Value("${recipe.import.base-path:/Users/<USER>/Desktop/HowToCook-master/dishes}")
    private String basePath;
    
    /**
     * 批量导入本地菜谱文件
     * @return 导入结果统计
     */
    public ImportResult batchImportLocalRecipes() {
        log.info("开始批量导入本地菜谱，基础路径：{}", basePath);
        
        ImportResult result = new ImportResult();
        File baseDir = new File(basePath);
        
        if (!baseDir.exists() || !baseDir.isDirectory()) {
            log.error("基础路径不存在或不是目录：{}", basePath);
            result.setErrorMessage("基础路径不存在或不是目录：" + basePath);
            return result;
        }
        
        // 遍历分类文件夹
        File[] categoryDirs = baseDir.listFiles(File::isDirectory);
        if (categoryDirs == null) {
            log.warn("基础路径下没有找到分类文件夹");
            return result;
        }
        
        for (File categoryDir : categoryDirs) {
            String categoryFolderName = categoryDir.getName();
            RecipeCategoryEnum category = RecipeCategoryEnum.getByFolderName(categoryFolderName);
            
            log.info("处理分类文件夹：{} -> {}", categoryFolderName, category.getName());
            
            ImportCategoryResult categoryResult = importRecipesFromCategory(categoryDir, category);
            result.addCategoryResult(categoryFolderName, categoryResult);
        }
        
        log.info("批量导入完成，总计：成功 {} 个，失败 {} 个", result.getTotalSuccess(), result.getTotalFailed());
        return result;
    }
    
    /**
     * 从指定分类文件夹导入菜谱
     */
    private ImportCategoryResult importRecipesFromCategory(File categoryDir, RecipeCategoryEnum category) {
        ImportCategoryResult result = new ImportCategoryResult();
        result.setCategoryName(category.getName());
        
        // 递归查找所有Markdown文件
        List<File> markdownFiles = findMarkdownFiles(categoryDir);
        log.info("在分类 {} 中找到 {} 个Markdown文件", category.getName(), markdownFiles.size());
        
        for (File markdownFile : markdownFiles) {
            try {
                String relativePath = getRelativePath(categoryDir, markdownFile);
                log.info("处理文件：{}", relativePath);
                
                // 读取文件内容
                String markdownContent = FileUtil.readString(markdownFile, StandardCharsets.UTF_8);
                
                if (StrUtil.isBlank(markdownContent)) {
                    log.warn("文件内容为空：{}", relativePath);
                    result.addFailedFile(relativePath, "文件内容为空");
                    continue;
                }
                
                // 解析菜谱
                ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
                if (recipe == null || StrUtil.isBlank(recipe.getName())) {
                    log.warn("菜谱解析失败或菜名为空：{}", relativePath);
                    result.addFailedFile(relativePath, "菜谱解析失败或菜名为空");
                    continue;
                }
                
                // 设置分类
                recipe.setCategory(category.getCode());
                
                // 保存到数据库
                Integer recipeId = recipeParserService.saveRecipeToDatabase(recipe);
                
                log.info("菜谱 {} 导入成功，ID：{}", recipe.getName(), recipeId);
                result.addSuccessFile(relativePath, recipe.getName(), recipeId);
                
            } catch (Exception e) {
                String relativePath = getRelativePath(categoryDir, markdownFile);
                log.error("导入文件失败：{}", relativePath, e);
                result.addFailedFile(relativePath, e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 递归查找Markdown文件
     */
    private List<File> findMarkdownFiles(File dir) {
        List<File> markdownFiles = new ArrayList<>();
        
        File[] files = dir.listFiles();
        if (files == null) {
            return markdownFiles;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归查找子目录
                markdownFiles.addAll(findMarkdownFiles(file));
            } else if (file.isFile() && file.getName().toLowerCase().endsWith(".md")) {
                markdownFiles.add(file);
            }
        }
        
        return markdownFiles;
    }
    
    /**
     * 获取相对路径
     */
    private String getRelativePath(File baseDir, File file) {
        String basePath = baseDir.getAbsolutePath();
        String filePath = file.getAbsolutePath();
        
        if (filePath.startsWith(basePath)) {
            return filePath.substring(basePath.length() + 1);
        }
        
        return file.getName();
    }
    
    /**
     * 导入单个菜谱文件
     */
    public ParsedRecipe importSingleRecipe(String categoryFolderName, String fileName) {
        // 移除文件名后缀进行分类映射
        String cleanCategoryName = categoryFolderName;
        if (categoryFolderName.endsWith(".md")) {
            cleanCategoryName = categoryFolderName.substring(0, categoryFolderName.length() - 3);
        }

        RecipeCategoryEnum category = RecipeCategoryEnum.getByFolderName(cleanCategoryName);

        File categoryDir = new File(basePath, categoryFolderName);
        if (!categoryDir.exists()) {
            throw new RuntimeException("分类文件夹不存在：" + categoryFolderName);
        }

        File markdownFile = findMarkdownFileByName(categoryDir, fileName);
        if (markdownFile == null) {
            throw new RuntimeException("菜谱文件不存在：" + fileName);
        }

        try {
            String markdownContent = FileUtil.readString(markdownFile, StandardCharsets.UTF_8);
            ParsedRecipe recipe = markdownRecipeParser.parseRecipe(markdownContent);
            recipe.setCategory(category.getCode());

            log.info("解析菜谱：{} -> 分类：{} ({})", recipe.getName(), category.getName(), category.getCode());
            return recipe;
        } catch (Exception e) {
            throw new RuntimeException("解析菜谱文件失败：" + fileName, e);
        }
    }
    
    /**
     * 根据文件名查找Markdown文件
     */
    private File findMarkdownFileByName(File dir, String fileName) {
        List<File> markdownFiles = findMarkdownFiles(dir);
        
        for (File file : markdownFiles) {
            if (file.getName().equals(fileName) || 
                file.getName().equals(fileName + ".md")) {
                return file;
            }
        }
        
        return null;
    }
    
    /**
     * 导入结果统计
     */
    public static class ImportResult {
        private final List<ImportCategoryResult> categoryResults = new ArrayList<>();
        private String errorMessage;
        
        public void addCategoryResult(String categoryName, ImportCategoryResult result) {
            categoryResults.add(result);
        }
        
        public int getTotalSuccess() {
            return categoryResults.stream().mapToInt(ImportCategoryResult::getSuccessCount).sum();
        }
        
        public int getTotalFailed() {
            return categoryResults.stream().mapToInt(ImportCategoryResult::getFailedCount).sum();
        }
        
        public List<ImportCategoryResult> getCategoryResults() {
            return categoryResults;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
    
    /**
     * 分类导入结果
     */
    public static class ImportCategoryResult {
        private String categoryName;
        private final List<String> successFiles = new ArrayList<>();
        private final List<String> failedFiles = new ArrayList<>();
        private final List<String> failedReasons = new ArrayList<>();
        
        public void addSuccessFile(String fileName, String recipeName, Integer recipeId) {
            successFiles.add(fileName + " -> " + recipeName + " (ID:" + recipeId + ")");
        }
        
        public void addFailedFile(String fileName, String reason) {
            failedFiles.add(fileName);
            failedReasons.add(reason);
        }
        
        public int getSuccessCount() {
            return successFiles.size();
        }
        
        public int getFailedCount() {
            return failedFiles.size();
        }
        
        // Getters and Setters
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        public List<String> getSuccessFiles() { return successFiles; }
        public List<String> getFailedFiles() { return failedFiles; }
        public List<String> getFailedReasons() { return failedReasons; }
    }
}
