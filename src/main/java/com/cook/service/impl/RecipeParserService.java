package com.cook.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cook.model.entity.CookIngredient;
import com.cook.model.entity.CookRecipe;
import com.cook.model.entity.CookRecipeIngredient;
import com.cook.model.entity.CookRecipeStep;
import com.cook.model.mapper.CookIngredientMapper;
import com.cook.model.mapper.CookRecipeIngredientRepository;
import com.cook.model.mapper.CookRecipeRepository;
import com.cook.model.mapper.CookRecipeStepRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱解析服务
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RecipeParserService {
    
    final CookRecipeRepository cookRecipeRepository;
    final CookIngredientMapper cookIngredientMapper;
    final CookRecipeIngredientRepository cookRecipeIngredientRepository;
    final CookRecipeStepRepository cookRecipeStepRepository;
    
    /**
     * 将解析后的菜谱保存到数据库
     */
    @Transactional
    public Integer saveRecipeToDatabase(MarkdownRecipeParser.ParsedRecipe parsedRecipe) {
        // 1. 创建菜谱基本信息
        CookRecipe recipe = CookRecipe.builder()
                .name(parsedRecipe.getName())
                .description(parsedRecipe.getDescription())
                .difficultyLevel(parsedRecipe.getDifficultyLevel())
                .servings(parsedRecipe.getServings())
                .category(parsedRecipe.getCategory() != null ? parsedRecipe.getCategory() : 0)
                .build();

        cookRecipeRepository.save(recipe);
        log.info("保存菜谱：{}", recipe.getName());

        // 2. 保存食材
        if (CollUtil.isNotEmpty(parsedRecipe.getIngredients())) {
            for (MarkdownRecipeParser.ParsedIngredient ingredientInfo : parsedRecipe.getIngredients()) {
                // 查找或创建食材
                CookIngredient ingredient = findOrCreateIngredient(ingredientInfo.getName());

                // 创建菜谱-食材关联
                CookRecipeIngredient recipeIngredient = CookRecipeIngredient.builder()
                        .recipeId(recipe.getId())
                        .ingredientId(ingredient.getId())
                        .quantity(ingredientInfo.getQuantity())
                        .type(ingredientInfo.getType())
                        .build();

                cookRecipeIngredientRepository.save(recipeIngredient);
            }
        }

        // 3. 保存制作步骤
        if (CollUtil.isNotEmpty(parsedRecipe.getSteps())) {
            for (int i = 0; i < parsedRecipe.getSteps().size(); i++) {
                MarkdownRecipeParser.ParsedStep stepInfo = parsedRecipe.getSteps().get(i);
                CookRecipeStep step = CookRecipeStep.builder()
                        .recipeId(recipe.getId())
                        .stepOrder(i + 1)
                        .description(stepInfo.getDescription())
                        .durationSeconds(stepInfo.getDurationSeconds())
                        .build();

                cookRecipeStepRepository.save(step);
            }
        }

        log.info("菜谱 {} 保存完成，共保存 {} 个食材，{} 个步骤",
                recipe.getName(),
                parsedRecipe.getIngredients().size(),
                parsedRecipe.getSteps().size());

        return recipe.getId();
    }

    /**
     * 解析白灼菜心菜谱并保存到数据库
     */
    @Transactional
    public void parseAndSaveBaiZhuoCaiXin() {
        // 1. 创建菜谱基本信息
        CookRecipe recipe = CookRecipe.builder()
                .name("白灼菜心")
                .description("白灼菜心是经典粤菜，白灼是粤菜的一种烹饪技法，用煮沸的水或汤将生的食物烫熟，称为白灼。这种烹饪手法能保持原有的鲜味，粤菜常用此法烹制虾和蔬菜。减肥或者是快速解决绿叶菜的绝佳方式。")
                .difficultyLevel(2)
                .servings(2)
                .build();
        
        cookRecipeRepository.save(recipe);
        log.info("保存菜谱：{}", recipe.getName());
        
        // 2. 创建和保存食材
        List<IngredientInfo> ingredients = createIngredientList();
        for (IngredientInfo ingredientInfo : ingredients) {
            // 查找或创建食材
            CookIngredient ingredient = findOrCreateIngredient(ingredientInfo.name);
            
            // 创建菜谱-食材关联
            CookRecipeIngredient recipeIngredient = CookRecipeIngredient.builder()
                    .recipeId(recipe.getId())
                    .ingredientId(ingredient.getId())
                    .quantity(ingredientInfo.quantity)
                    .type(ingredientInfo.type)
                    .build();
            
            cookRecipeIngredientRepository.save(recipeIngredient);
        }
        
        // 3. 创建制作步骤
        List<StepInfo> steps = createStepList();
        for (int i = 0; i < steps.size(); i++) {
            StepInfo stepInfo = steps.get(i);
            CookRecipeStep step = CookRecipeStep.builder()
                    .recipeId(recipe.getId())
                    .stepOrder(i + 1)
                    .description(stepInfo.description)
                    .durationSeconds(stepInfo.durationSeconds)
                    .build();
            
            cookRecipeStepRepository.save(step);
        }
        
        log.info("白灼菜心菜谱解析完成，共保存 {} 个食材，{} 个步骤", ingredients.size(), steps.size());
    }
    
    /**
     * 查找或创建食材（处理UNIQUE约束）
     */
    private CookIngredient findOrCreateIngredient(String name) {
        // 先查找是否已存在
        List<CookIngredient> existing = cookIngredientMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CookIngredient>()
                        .eq(CookIngredient::getName, name)
        );

        if (CollUtil.isNotEmpty(existing)) {
            return existing.get(0);
        }

        // 不存在则创建，处理可能的并发插入导致的UNIQUE约束冲突
        try {
            CookIngredient ingredient = CookIngredient.builder()
                    .name(name)
                    .build();
            cookIngredientMapper.insert(ingredient);
            return ingredient;
        } catch (Exception e) {
            // 如果插入失败（可能是UNIQUE约束冲突），再次查询
            log.warn("插入食材 {} 失败，可能已存在，重新查询", name);
            List<CookIngredient> retry = cookIngredientMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<CookIngredient>()
                            .eq(CookIngredient::getName, name)
            );
            if (CollUtil.isNotEmpty(retry)) {
                return retry.get(0);
            }
            throw new RuntimeException("创建食材失败: " + name, e);
        }
    }
    
    /**
     * 创建食材列表
     */
    private List<IngredientInfo> createIngredientList() {
        List<IngredientInfo> ingredients = new ArrayList<>();
        
        // 主料
        ingredients.add(new IngredientInfo("菜心", "250g", "main"));
        
        // 调料
        ingredients.add(new IngredientInfo("生抽", "5g", "seasoning"));
        ingredients.add(new IngredientInfo("蚝油", "5g", "seasoning"));
        ingredients.add(new IngredientInfo("盐", "5g", "seasoning"));
        ingredients.add(new IngredientInfo("糖", "3g", "seasoning"));
        ingredients.add(new IngredientInfo("食用油", "10g", "seasoning"));
        ingredients.add(new IngredientInfo("大蒜", "4-5瓣", "seasoning"));
        ingredients.add(new IngredientInfo("小米辣", "1-2根", "seasoning"));
        ingredients.add(new IngredientInfo("清水", "100g", "supplement"));
        
        return ingredients;
    }
    
    /**
     * 创建步骤列表
     */
    private List<StepInfo> createStepList() {
        List<StepInfo> steps = new ArrayList<>();
        
        steps.add(new StepInfo("菜心洗净，去除根部比较硬或老的地方。用刀刮菜心根茎部分，刮掉外面那层比较硬的，菜心内部更可口，但要注意根茎白灼时长，时间太长的话根茎不脆了", null));
        
        steps.add(new StepInfo("大蒜切成蒜末，有洋葱顺便加了点洋葱", null));
        
        steps.add(new StepInfo("调制灵魂料汁：生抽5g、蚝油5g，加3g糖和100g清水半碗成一碗汁儿", null));
        
        steps.add(new StepInfo("一锅500ml清水加5g盐和10g食用油烧开", null));
        
        steps.add(new StepInfo("将菜心根茎在沸水中烫1分钟，直到根茎颜色变成深绿。再将整个菜心放到锅中烫熟1分钟，捞起来码入盘中", 120));
        
        steps.add(new StepInfo("开另一小锅将兑好的料汁倒入，小火烧开，放入一半的蒜末，一点点姜丝和小米椒碎。先在锅底倒油，五成热后倒入蒜末、洋葱，稍稍爆香后再加入料汁，加入小米辣煮开", null));
        
        steps.add(new StepInfo("料汁稍微收汁，煮沸后稍等十来秒，后直接浇在菜心上，不要特别多，但蒜末还是很给力的不要少蒜", 10));
        
        return steps;
    }
    
    /**
     * 食材信息内部类
     */
    private static class IngredientInfo {
        String name;
        String quantity;
        String type;
        
        IngredientInfo(String name, String quantity, String type) {
            this.name = name;
            this.quantity = quantity;
            this.type = type;
        }
    }
    
    /**
     * 步骤信息内部类
     */
    private static class StepInfo {
        String description;
        Integer durationSeconds;
        
        StepInfo(String description, Integer durationSeconds) {
            this.description = description;
            this.durationSeconds = durationSeconds;
        }
    }
}
