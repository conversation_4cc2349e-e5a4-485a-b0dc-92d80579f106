package com.cook.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.cook.model.dto.BaiDuVersionsResult;
import com.cook.service.client.BaiduServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 百度食材识别服务
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BaiduIngredientService {
    
    final BaiduServiceClient baiduServiceClient;
    
    @Value("${baidu.ingredient.use-feign:true}")
    private Boolean useFeign;
    
    /**
     * 调用百度食材识别接口
     * @param accessToken 访问令牌
     * @param imageBase64 图片base64数据
     * @return 识别结果
     */
    public BaiDuVersionsResult classifyIngredient(String accessToken, String imageBase64) {
        if (useFeign) {
            return classifyIngredientWithFeign(accessToken, imageBase64);
        } else {
            return classifyIngredientWithHttp(accessToken, imageBase64);
        }
    }
    
    /**
     * 使用Feign客户端调用
     */
    private BaiDuVersionsResult classifyIngredientWithFeign(String accessToken, String imageBase64) {
        try {
            log.debug("使用Feign客户端调用百度食材识别接口");
            return baiduServiceClient.classifyIngredient(accessToken, imageBase64);
        } catch (Exception e) {
            log.error("Feign调用失败，尝试使用HTTP方式", e);
            return classifyIngredientWithHttp(accessToken, imageBase64);
        }
    }
    
    /**
     * 使用HttpUtil调用（备用方案）
     */
    private BaiDuVersionsResult classifyIngredientWithHttp(String accessToken, String imageBase64) {
        try {
            log.debug("使用HttpUtil调用百度食材识别接口");
            String url = "https://aip.baidubce.com/rest/2.0/image-classify/v1/classify/ingredient?access_token=" + accessToken;
            
            Map<String, Object> param = new HashMap<>();
            param.put("image", imageBase64);
            
            String result = HttpUtil.post(url, param);
            log.debug("HTTP调用结果：{}", result);
            
            if (StrUtil.isBlank(result)) {
                throw new RuntimeException("百度API返回空结果");
            }
            
            return JSONUtil.toBean(result, BaiDuVersionsResult.class);
        } catch (Exception e) {
            log.error("HTTP调用也失败了", e);
            throw new RuntimeException("百度食材识别接口调用失败", e);
        }
    }
}
