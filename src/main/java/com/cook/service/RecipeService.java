package com.cook.service;

import com.cook.model.base.ResultData;
import com.cook.model.dto.RecipeSearchDto;
import com.cook.model.vo.PageResult;
import com.cook.model.vo.RecipeDetailVo;
import com.cook.model.vo.RecipeListVo;

/**
 * <AUTHOR>
 * @description 菜谱服务接口
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
public interface RecipeService {
    
    /**
     * 搜索菜谱（支持菜名和食材名搜索）
     * @param dto 搜索参数
     * @return 菜谱列表
     */
    ResultData<PageResult<RecipeListVo>> searchRecipes(RecipeSearchDto dto);
    
    /**
     * 根据菜谱ID获取菜谱详情
     * @param recipeId 菜谱ID
     * @return 菜谱详情
     */
    ResultData<RecipeDetailVo> getRecipeDetail(Integer recipeId);
}
