package com.cook.service.client;

import com.cook.common.config.BaiduFeignConfig;
import com.cook.model.dto.BaiDuTokenResult;
import com.cook.model.dto.BaiDuVersionsResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @see
 * @since 2025/6/23 20:30
 * {@inheritDoc}
 */
@FeignClient(name = "baiduServiceClient", configuration = BaiduFeignConfig.class, url = "https://aip.baidubce.com")
public interface BaiduServiceClient {
    @PostMapping(value = "/oauth/2.0/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    BaiDuTokenResult getAccessToken(
            @RequestParam("grant_type") String grantType,
            @RequestParam("client_id") String clientId,
            @RequestParam("client_secret") String clientSecret
    );

    /**
     * 食材识别接口
     * @param accessToken 访问令牌
     * @param image base64编码的图片数据
     * @return 识别结果
     */
    @PostMapping(value = "/rest/2.0/image-classify/v1/classify/ingredient",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    BaiDuVersionsResult classifyIngredient(
            @RequestParam("access_token") String accessToken,
            @RequestParam("image") String image);

    /**
     * 食材识别接口（带更多参数）
     * @param accessToken 访问令牌
     * @param image base64编码的图片数据
     * @param topNum 返回结果top数，默认为5
     * @return 识别结果
     */
    @PostMapping(value = "/rest/2.0/image-classify/v1/classify/ingredient",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    BaiDuVersionsResult classifyIngredientWithTopNum(
            @RequestParam("access_token") String accessToken,
            @RequestParam("image") String image,
            @RequestParam(value = "top_num", required = false) Integer topNum);

    /**
     * 食材识别接口（使用URL）
     * @param accessToken 访问令牌
     * @param url 图片URL地址
     * @return 识别结果
     */
    @PostMapping(value = "/rest/2.0/image-classify/v1/classify/ingredient",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    BaiDuVersionsResult classifyIngredientByUrl(
            @RequestParam("access_token") String accessToken,
            @RequestParam("url") String url);

    /**
     * 食材识别接口（使用Map参数，更灵活）
     * @param accessToken 访问令牌
     * @param params 请求参数Map
     * @return 识别结果
     */
    @PostMapping(value = "/rest/2.0/image-classify/v1/classify/ingredient",
            consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    BaiDuVersionsResult classifyIngredientWithMap(
            @RequestParam("access_token") String accessToken,
            @RequestParam Map<String, Object> params);

}
