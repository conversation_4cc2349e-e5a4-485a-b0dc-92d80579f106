package com.cook.service.client;

import com.cook.common.config.FeignConfig;
import com.cook.model.dto.WxSessionResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description 微信小程序服务客户端
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@FeignClient(name = "wxServiceClient", configuration = FeignConfig.class, url = "https://api.weixin.qq.com")
public interface WxServiceClient {
    
    /**
     * 微信小程序code2session接口
     * 通过登录凭证获取用户openid和session_key
     */
    @GetMapping("/sns/jscode2session")
    WxSessionResult code2Session(
            @RequestParam("appid") String appid,
            @RequestParam("secret") String secret,
            @RequestParam("js_code") String jsCode,
            @RequestParam("grant_type") String grantType
    );
}
