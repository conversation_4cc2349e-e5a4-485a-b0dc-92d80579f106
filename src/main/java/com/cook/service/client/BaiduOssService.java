package com.cook.service.client;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baidubce.auth.BceCredentials;
import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.baidubce.services.bos.model.ObjectMetadata;
import com.baidubce.services.bos.model.PutObjectResponse;
import com.baidubce.services.sts.StsClient;
import com.baidubce.services.sts.model.GetSessionTokenRequest;
import com.baidubce.services.sts.model.GetSessionTokenResponse;
import com.cook.common.config.BaiduOssClientConfig;
import com.cook.common.util.CookIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.net.URL;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BaiduOssService {
    final BaiduOssClientConfig baiduOssClientConfig;

    /**
     * 上传文件
     * @param file
     * @return
     */
    public String uploadFile(File file, String fileType) {
        BosClient client = null;
        try {
            long id = CookIdUtil.getId();
            String objectKey = getObjectKey(id, fileType);

            log.info("开始上传文件到百度OSS，ObjectKey: {}, 文件大小: {} bytes", objectKey, file.length());

            client = baiduOssClientConfig.getOssClient();

            // 验证配置
            validateOssConfig();

            // 以文件形式上传Object
            PutObjectResponse put = client.putObject(baiduOssClientConfig.getOssBucketName(), objectKey, file);
            log.info("文件上传成功，ETag: {}, ObjectKey: {}", put.getETag(), objectKey);

            return objectKey;

        } catch (Exception e) {
            log.error("文件上传到百度OSS失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } finally {
            // 关闭客户端
            if (client != null) {
                try {
                    client.shutdown();
                } catch (Exception e) {
                    log.warn("关闭BOS客户端失败", e);
                }
            }
        }
    }

    public long uploadFile(InputStream inputStream, String fileType) {
        long id = CookIdUtil.getId();
        String objectKey = getObjectKey(id, fileType);
        BosClient client = baiduOssClientConfig.getOssClient();
        // 以数据流形式上传Object
        PutObjectResponse put = client.putObject(baiduOssClientConfig.getOssBucketName(), objectKey, inputStream);
        log.info(put.getETag());
        // 关闭客户端
        client.shutdown();
        log.info("文件ID：{}",  id);
        return id;
    }


    /**
     * 生成文件访问URL（使用主账号凭证，避免STS权限问题）
     */
    public String generateUrl(String objectKey) {
        BosClient bosClient = null;

        try {
            // 直接使用主账号凭证，避免STS权限问题
            bosClient = baiduOssClientConfig.getOssClient();

            // 生成预签名URL，有效期1小时（3600秒）
            URL url = bosClient.generatePresignedUrl(baiduOssClientConfig.getOssBucketName(), objectKey, 3600);
            String urlString = url.toString();
            log.info("文件访问地址生成成功，ObjectKey：{}，URL长度：{}", objectKey, urlString);
            return urlString;

        } catch (Exception e) {
            log.error("生成文件访问地址失败，ObjectKey：{}", objectKey, e);
            throw new RuntimeException("生成文件访问地址失败：" + e.getMessage(), e);
        } finally {
            // 确保客户端被正确关闭
            if (bosClient != null) {
                try {
                    bosClient.shutdown();
                } catch (Exception e) {
                    log.warn("关闭BOS客户端失败", e);
                }
            }
        }
    }


    private static String getObjectKey(long id, String fileType) {
        String date = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        return "photos/"+date+"/" + id + "." + fileType ;
    }

    /**
     * 验证OSS配置
     */
    private void validateOssConfig() {
        if (baiduOssClientConfig.getOssAccessKeyId() == null || baiduOssClientConfig.getOssAccessKeyId().trim().isEmpty()) {
            throw new IllegalArgumentException("百度OSS AccessKeyId未配置");
        }
        if (baiduOssClientConfig.getOssSecretAccessKey() == null || baiduOssClientConfig.getOssSecretAccessKey().trim().isEmpty()) {
            throw new IllegalArgumentException("百度OSS SecretAccessKey未配置");
        }
        if (baiduOssClientConfig.getOssEndpoint() == null || baiduOssClientConfig.getOssEndpoint().trim().isEmpty()) {
            throw new IllegalArgumentException("百度OSS Endpoint未配置");
        }
        if (baiduOssClientConfig.getOssBucketName() == null || baiduOssClientConfig.getOssBucketName().trim().isEmpty()) {
            throw new IllegalArgumentException("百度OSS BucketName未配置");
        }

        log.info("百度OSS配置验证通过 - Endpoint: {}, Bucket: {}, AccessKeyId: {}***",
                baiduOssClientConfig.getOssEndpoint(),
                baiduOssClientConfig.getOssBucketName(),
                baiduOssClientConfig.getOssAccessKeyId().substring(0, Math.min(8, baiduOssClientConfig.getOssAccessKeyId().length())));
    }
}
