package com.cook.service;

import com.cook.model.base.ResultData;
import com.cook.model.dto.CookImageInfoDto;
import com.cook.model.dto.CookVersionRecordPageDto;
import com.cook.model.vo.CookAiVersionInfoVo;
import com.cook.model.vo.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @see
 * @since 2025/6/14 12:22
 * {@inheritDoc}
 */
public interface CookInfoService {
    ResultData<List<CookAiVersionInfoVo>> upload(CookImageInfoDto dto);

    /**
     * 多文件上传接口（支持MultipartFile）
     * @param files 上传的文件数组
     * @param description 可选的描述信息
     * @param enableAiRecognition 是否启用AI识别
     * @return 识别结果列表
     */
    ResultData<List<CookAiVersionInfoVo>> uploadMultipart(MultipartFile[] files, String description, Boolean enableAiRecognition);

    /**
     * 分页查询用户的CookVersionRecord数据
     * @param dto 分页查询参数
     * @return 分页结果
     */
    ResultData<PageResult<CookAiVersionInfoVo>> getVersionRecordsPage(CookVersionRecordPageDto dto);
}
