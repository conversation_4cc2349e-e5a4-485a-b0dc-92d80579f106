package com.cook.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱详情响应VO
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecipeDetailVo {
    
    /**
     * 菜谱ID
     */
    private Integer id;
    
    /**
     * 菜名
     */
    private String name;
    
    /**
     * 简介
     */
    private String description;
    
    /**
     * 难度等级（1-3）
     */
    private Integer difficultyLevel;
    
    /**
     * 难度等级描述
     */
    private String difficultyLevelDesc;
    
    /**
     * 份数
     */
    private Integer servings;
    
    /**
     * 食材列表
     */
    private List<RecipeIngredientVo> ingredients;
    
    /**
     * 制作步骤列表
     */
    private List<RecipeStepVo> steps;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecipeIngredientVo {
        /**
         * 食材ID
         */
        private Integer ingredientId;
        
        /**
         * 食材名称
         */
        private String ingredientName;
        
        /**
         * 数量
         */
        private String quantity;
        
        /**
         * 类型：main-主料，supplement-辅料，seasoning-调料
         */
        private String type;
        
        /**
         * 类型描述
         */
        private String typeDesc;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecipeStepVo {
        /**
         * 步骤ID
         */
        private Integer id;
        
        /**
         * 步骤顺序
         */
        private Integer stepOrder;
        
        /**
         * 步骤描述
         */
        private String description;
        
        /**
         * 持续秒数
         */
        private Integer durationSeconds;
        
        /**
         * 持续时间描述
         */
        private String durationDesc;
    }
}
