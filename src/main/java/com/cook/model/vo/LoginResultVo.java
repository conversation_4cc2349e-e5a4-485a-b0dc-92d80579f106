package com.cook.model.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 登录结果响应
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResultVo {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 访问令牌
     */
    private String token;
    
    /**
     * 令牌过期时间（时间戳）
     */
    private Long expireTime;
    
    /**
     * 用户信息
     */
    private UserInfoVo userInfo;

    /**
     * 用户唯一标识
     */
    private String openid;

    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 用户在开放平台的唯一标识符（可选）
     */
    private String unionid;
}
