package com.cook.model.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cook.model.entity.CookRecipeIngredient;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱-食材映射Mapper接口
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
public interface CookRecipeIngredientMapper extends BaseMapper<CookRecipeIngredient> {
    
    /**
     * 根据菜谱ID查询食材信息（包含食材名称）
     * @param recipeId 菜谱ID
     * @return 食材信息列表
     */
    @Select("SELECT ri.*, i.name as ingredient_name " +
            "FROM cook_recipe_ingredient ri " +
            "INNER JOIN cook_ingredient i ON ri.ingredient_id = i.id " +
            "WHERE ri.recipe_id = #{recipeId} " +
            "ORDER BY ri.type, ri.id")
    List<CookRecipeIngredient> findIngredientsByRecipeId(@Param("recipeId") Integer recipeId);
}
