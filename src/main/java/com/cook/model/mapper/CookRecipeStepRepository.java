package com.cook.model.mapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cook.model.entity.CookRecipeStep;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 制作步骤Repository
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class CookRecipeStepRepository extends ServiceImpl<CookRecipeStepMapper, CookRecipeStep> {
}
