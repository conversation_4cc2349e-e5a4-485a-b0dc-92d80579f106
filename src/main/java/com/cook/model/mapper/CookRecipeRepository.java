package com.cook.model.mapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cook.model.entity.CookRecipe;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱Repository
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class CookRecipeRepository extends ServiceImpl<CookRecipeMapper, CookRecipe> {
    
    /**
     * 根据食材名称查询相关菜谱
     */
    public List<CookRecipe> findRecipesByIngredientNames(List<String> ingredientNames) {
        return baseMapper.findRecipesByIngredientNames(ingredientNames);
    }
}
