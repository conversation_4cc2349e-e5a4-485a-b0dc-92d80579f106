package com.cook.model.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cook.model.entity.CookRecipe;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱Mapper接口
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
public interface CookRecipeMapper extends BaseMapper<CookRecipe> {
    
    /**
     * 根据食材名称查询相关菜谱
     * @param ingredientNames 食材名称列表
     * @return 菜谱列表
     */
    @Select("<script>" +
            "SELECT DISTINCT r.* FROM cook_recipe r " +
            "INNER JOIN cook_recipe_ingredient ri ON r.id = ri.recipe_id " +
            "INNER JOIN cook_ingredient i ON ri.ingredient_id = i.id " +
            "WHERE i.name IN " +
            "<foreach collection='ingredientNames' item='name' open='(' separator=',' close=')'>" +
            "#{name}" +
            "</foreach>" +
            "</script>")
    List<CookRecipe> findRecipesByIngredientNames(@Param("ingredientNames") List<String> ingredientNames);
}
