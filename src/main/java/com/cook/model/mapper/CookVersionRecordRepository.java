package com.cook.model.mapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cook.model.entity.CookVersionRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class CookVersionRecordRepository extends ServiceImpl<CookVersionRecordMapper, CookVersionRecord> {
}
