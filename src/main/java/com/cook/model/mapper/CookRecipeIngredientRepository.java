package com.cook.model.mapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cook.model.entity.CookRecipeIngredient;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱-食材映射Repository
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class CookRecipeIngredientRepository extends ServiceImpl<CookRecipeIngredientMapper, CookRecipeIngredient> {
    
    /**
     * 根据菜谱ID查询食材信息
     */
    public List<CookRecipeIngredient> findIngredientsByRecipeId(Integer recipeId) {
        return baseMapper.findIngredientsByRecipeId(recipeId);
    }
}
