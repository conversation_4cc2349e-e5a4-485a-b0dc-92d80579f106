package com.cook.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 菜谱-食材映射实体类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cook_recipe_ingredient")
public class CookRecipeIngredient {
    
    /**
     * 映射ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 菜谱ID
     */
    private Integer recipeId;
    
    /**
     * 食材ID
     */
    private Integer ingredientId;
    
    /**
     * 数量（如"半颗"、"2个"、"10ml"等）
     */
    private String quantity;
    
    /**
     * 主料/辅料/调料
     */
    private String type;

    /**
     * 食材名称（查询时关联获取，不存储在数据库中）
     */
    @TableField(exist = false)
    private String ingredientName;
}
