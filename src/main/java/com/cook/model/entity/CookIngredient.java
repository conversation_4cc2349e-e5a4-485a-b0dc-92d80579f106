package com.cook.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 食材实体类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cook_ingredient")
public class CookIngredient {
    
    /**
     * 食材ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 食材名，如"包菜"、"盐"、"鸡蛋"
     */
    private String name;
}
