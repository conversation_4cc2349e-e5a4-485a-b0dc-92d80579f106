package com.cook.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 菜谱实体类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cook_recipe")
public class CookRecipe {
    
    /**
     * 菜谱ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 菜名
     */
    private String name;
    
    /**
     * 简介
     */
    private String description;
    
    /**
     * 难度等级（1-3）
     */
    private Integer difficultyLevel;
    
    /**
     * 份数
     */
    private Integer servings;

    /**
     * 菜品分类：0-未知 1-荤菜 2-蔬菜 3-汤 4-海鲜 5-甜点 6-饮品 7-主食 8-调料 9-早餐
     */
    private Integer category;
}
