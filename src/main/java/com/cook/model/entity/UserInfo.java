package com.cook.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 用户信息实体类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_info")
public class UserInfo {
    
    /**
     * 用户ID，主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 微信小程序openid
     */
    private String openid;
    
    /**
     * 微信小程序unionid（可选）
     */
    private String unionid;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 用户头像URL
     */
    private String avatarUrl;
    
    /**
     * 性别：0-未知，1-男，2-女
     */
    private Integer gender;
    
    /**
     * 国家
     */
    private String country;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 语言
     */
    private String language;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 用户状态：0-正常，1-禁用
     */
    private Integer status;
    
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
