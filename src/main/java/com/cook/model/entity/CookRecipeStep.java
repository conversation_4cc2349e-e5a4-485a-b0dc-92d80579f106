package com.cook.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 制作步骤实体类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cook_recipe_step")
public class CookRecipeStep {
    
    /**
     * 步骤ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 菜谱ID
     */
    private Integer recipeId;
    
    /**
     * 步骤顺序
     */
    private Integer stepOrder;
    
    /**
     * 步骤描述
     */
    private String description;
    
    /**
     * 持续秒数（可选）
     */
    private Integer durationSeconds;
}
