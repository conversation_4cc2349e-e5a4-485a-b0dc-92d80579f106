package com.cook.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CookVersionRecord {
    private Long id;

    private Long userId;

    private String uploadParams;

    private String resultParams;

    private Date createTime;

    private Date updateTime;


}
