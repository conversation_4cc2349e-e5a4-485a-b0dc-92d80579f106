package com.cook.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/23 21:02
 */
@Data
public class BaiDuVersionsResult {
    @JsonAlias("result_num")
    private Integer resultNum;

    private List<VersionInfo> result;

    @JsonAlias("log_id")
    private String logId;

    @Data
    public static class VersionInfo{
        private Double score;
        private String name;
    }
}
