package com.cook.model.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @description 多文件上传DTO
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/7/8
 */
@Data
public class CookMultiFileUploadDto {
    
    /**
     * 上传的文件列表
     */
    @NotEmpty(message = "文件列表不能为空")
    private MultipartFile[] files;
    
    /**
     * 可选的描述信息
     */
    private String description;
    
    /**
     * 是否启用百度AI识别
     */
    private Boolean enableAiRecognition = true;
}
