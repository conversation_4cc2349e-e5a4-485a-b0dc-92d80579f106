package com.cook.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/23 21:02
 */
@Data
public class BaiDuTokenResult {
    @JsonAlias("refresh_token")
    private String refreshToken;

    @JsonAlias("expires_in")
    private Integer expiresIn;

    @JsonAlias("session_key")
    private String sessionKey;

    @JsonAlias("access_token")
    private String accessToken;

    @JsonAlias("session_secret")
    private String sessionSecret;

}
