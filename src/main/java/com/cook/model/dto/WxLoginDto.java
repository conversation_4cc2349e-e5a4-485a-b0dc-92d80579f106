package com.cook.model.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 微信小程序登录请求参数
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
public class WxLoginDto {
    
    /**
     * 微信小程序登录凭证code
     */
    @NotBlank(message = "登录凭证不能为空")
    private String code;
    
    /**
     * 用户信息（可选，用于更新用户资料）
     */
    private UserInfoDto userInfo;
    
    @Data
    public static class UserInfoDto {
        /**
         * 用户昵称
         */
        private String nickName;
        
        /**
         * 用户头像URL
         */
        private String avatarUrl;
        
        /**
         * 性别：0-未知，1-男，2-女
         */
        private Integer gender;
        
        /**
         * 国家
         */
        private String country;
        
        /**
         * 省份
         */
        private String province;
        
        /**
         * 城市
         */
        private String city;
        
        /**
         * 语言
         */
        private String language;
    }
}
