package com.cook.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/23 21:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaiDuVersionsDto {
    private String image;
    private String url;
    @JsonAlias("top_num")
    private Integer topNum;


}
