package com.cook.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 微信小程序code2session接口响应
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
public class WxSessionResult {
    
    /**
     * 用户唯一标识
     */
    private String openid;
    
    /**
     * 会话密钥
     */
    @JsonAlias("session_key")
    private String sessionKey;
    
    /**
     * 用户在开放平台的唯一标识符（可选）
     */
    private String unionid;
    
    /**
     * 错误码
     */
    private Integer errcode;
    
    /**
     * 错误信息
     */
    private String errmsg;
}
