package com.cook.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱搜索请求参数
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
public class RecipeSearchDto {
    
    /**
     * 菜谱名称（模糊匹配）
     */
    private String recipeName;
    
    /**
     * 食材名称列表
     */
    private List<String> ingredientNames;
    
    /**
     * 难度等级（1-3）
     */
    private Integer difficultyLevel;
    
    /**
     * 页码，从1开始
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
}
