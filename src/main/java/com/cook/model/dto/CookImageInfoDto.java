package com.cook.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/14 12:14
 */
@Data
public class CookImageInfoDto {
  private List<Info> files;

  @Data
  public static class Info{
   @NotBlank
   private String fileName;
   private Long fileId;

   @NotNull
   @Positive
   private Long fileSize;

   @NotNull
   private String fileData;
  }
}
