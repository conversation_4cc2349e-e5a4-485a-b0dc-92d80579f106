# 微信登录接口问题分析与解决方案

## 问题描述

微信小程序登录接口 `/user/wx-login` 执行失败，出现以下错误：

```
feign.codec.DecodeException: Could not extract response: no suitable HttpMessageConverter found for response type [class com.cook.model.dto.WxSessionResult] and content type [text/plain]
```

## 错误分析

### 1. 错误根本原因

从错误日志可以看出：

1. **微信API响应内容正确**：
   ```json
   {"session_key":"0frD1rVexR3QlrfvzJ+UAQ==","openid":"on3U95Bntn51lW_QQ4PZvm2y1GHc"}
   ```

2. **Content-Type不匹配**：
   - 微信API返回的 Content-Type 是 `text/plain`
   - 但 Feign 客户端期望的是 `application/json`
   - Spring 的 HttpMessageConverter 无法处理这种不匹配

### 2. 技术细节

- **接口路径**：`https://api.weixin.qq.com/sns/jscode2session`
- **返回格式**：实际是JSON，但Content-Type标记为text/plain
- **Feign配置**：默认只支持application/json类型的响应解析

## 解决方案

### 方案一：修改Feign配置（推荐）

修改 `FeignConfig.java`，添加自定义解码器支持 `text/plain` 类型：

```java
@Bean
public Decoder feignDecoder() {
    // 创建支持text/plain的Jackson转换器
    MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
    converter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_JSON,
            MediaType.TEXT_PLAIN,  // 添加对text/plain的支持
            new MediaType("application", "*+json")
    ));
    
    ObjectFactory<HttpMessageConverters> messageConverters = () -> 
        new HttpMessageConverters(converter);
    
    return new ResponseEntityDecoder(new SpringDecoder(messageConverters));
}
```

### 方案二：使用RestTemplate替代Feign

如果Feign配置修改不生效，可以考虑使用RestTemplate：

```java
@Service
public class WxApiService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public WxSessionResult code2Session(String appid, String secret, String jsCode, String grantType) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={jsCode}&grant_type={grantType}";
        
        // RestTemplate会自动处理text/plain到JSON的转换
        return restTemplate.getForObject(url, WxSessionResult.class, appid, secret, jsCode, grantType);
    }
}
```

### 方案三：自定义Feign解码器

创建专门的解码器处理微信API：

```java
public class WxApiDecoder implements Decoder {
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Object decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        if (response.body() == null) {
            return null;
        }
        
        String bodyText = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        
        // 无论Content-Type是什么，都尝试解析为JSON
        try {
            return objectMapper.readValue(bodyText, (Class<?>) type);
        } catch (JsonProcessingException e) {
            throw new DecodeException(response.status(), "Failed to decode JSON", response.request(), e);
        }
    }
}
```

## 实施步骤

### 1. 立即修复（方案一）

已经修改了 `FeignConfig.java` 文件，添加了对 `text/plain` 类型的支持。

### 2. 验证修复

重启应用后测试微信登录接口：

```bash
curl -X POST "http://localhost:8082/user/wx-login" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "valid_wx_code_from_miniapp",
    "userInfo": {
      "nickName": "测试用户",
      "avatarUrl": "https://example.com/avatar.jpg"
    }
  }'
```

### 3. 监控日志

观察以下日志输出：
- Feign请求日志
- 微信API响应日志
- 解码过程日志

## 预防措施

### 1. 添加单元测试

```java
@Test
public void testWxApiResponseDecoding() {
    String jsonResponse = "{\"session_key\":\"test\",\"openid\":\"test_openid\"}";
    // 测试解码器是否能正确处理text/plain类型的JSON响应
}
```

### 2. 配置监控

添加对微信API调用的监控：
- 响应时间监控
- 错误率监控
- Content-Type统计

### 3. 备用方案

保留RestTemplate作为备用方案，在Feign失败时自动切换。

## 相关配置

### 微信小程序配置

```yaml
wx:
  miniapp:
    appid: wxbe4f9d4bae7148d2
    secret: efb3ff0e65fa5224536aa0d1341fa2be
```

### Feign日志配置

```yaml
logging:
  level:
    com.cook.service.client.WxServiceClient: DEBUG
```

## 已实施的解决方案

### 1. 修改Feign配置（主要方案）

已修改 `FeignConfig.java`，添加了对 `text/plain` 类型的支持：

```java
@Bean
public Decoder feignDecoder() {
    MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
    converter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_JSON,
            MediaType.TEXT_PLAIN,  // 关键修复
            new MediaType("application", "*+json")
    ));

    ObjectFactory<HttpMessageConverters> messageConverters = () ->
        new HttpMessageConverters(converter);

    return new ResponseEntityDecoder(new SpringDecoder(messageConverters));
}
```

### 2. 添加RestTemplate备用方案

创建了 `WxApiService` 类，使用 RestTemplate 作为备用方案：

```java
@Service
public class WxApiService {
    final RestTemplate restTemplate;

    public WxSessionResult code2Session(String appid, String secret, String jsCode, String grantType) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={jsCode}&grant_type={grantType}";
        return restTemplate.getForObject(url, WxSessionResult.class, appid, secret, jsCode, grantType);
    }
}
```

### 3. 添加配置开关

在 `application.yml` 中添加了开关配置：

```yaml
wx:
  miniapp:
    appid: wxbe4f9d4bae7148d2
    secret: efb3ff0e65fa5224536aa0d1341fa2be
    use-feign: false  # 当前使用RestTemplate，避免风险
```

### 4. 修改服务实现

在 `UserInfoServiceImpl` 中添加了双重保障：

```java
WxSessionResult sessionResult;
if (useFeign) {
    log.info("使用Feign客户端调用微信API");
    sessionResult = wxServiceClient.code2Session(wxAppid, wxSecret, dto.getCode(), "authorization_code");
} else {
    log.info("使用RestTemplate调用微信API");
    sessionResult = wxApiService.code2Session(wxAppid, wxSecret, dto.getCode(), "authorization_code");
}
```

## 测试验证

创建了测试脚本 `scripts/test-wx-login.sh`，可以用来验证修复效果。

## 部署建议

1. **立即生效**：当前配置使用 RestTemplate，可以立即解决问题
2. **逐步验证**：后续可以将 `use-feign` 改为 `true` 来测试 Feign 修复是否有效
3. **监控观察**：部署后观察日志，确认没有解码错误

## 总结

通过双重保障的方式解决了微信登录接口问题：
- **主要方案**：修复Feign配置支持text/plain类型
- **备用方案**：使用RestTemplate确保服务可用
- **配置开关**：可以灵活切换两种方案

这种方式既解决了当前问题，又为未来类似问题提供了解决思路。
