#!/bin/bash

# 本地菜谱导入测试脚本
# 使用方法: ./test-local-import.sh [JWT_TOKEN]

BASE_URL="http://localhost:8080"
JWT_TOKEN=${1:-"your_jwt_token_here"}

echo "=== 本地菜谱导入测试脚本 ==="
echo "Base URL: $BASE_URL"
echo "JWT Token: ${JWT_TOKEN:0:20}..."
echo ""

# 1. 测试食材解析优化
echo "1. 测试食材解析优化..."
INGREDIENT_RESULT=$(curl -s -X GET "$BASE_URL/recipe-parser/test-ingredient-parsing" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$INGREDIENT_RESULT" | jq '.'

# 验证食材标准化效果
INGREDIENTS=$(echo "$INGREDIENT_RESULT" | jq -r '.data.ingredients[].name')
echo "解析出的标准化食材："
echo "$INGREDIENTS"

if [[ "$INGREDIENTS" == *"辣椒"* ]] && [[ "$INGREDIENTS" == *"蒜"* ]] && [[ "$INGREDIENTS" == *"菜心"* ]]; then
    echo "✅ 食材标准化功能正常"
else
    echo "❌ 食材标准化功能异常"
fi
echo ""

# 2. 测试无序号步骤解析
echo "2. 测试无序号步骤解析..."
curl -s -X GET "$BASE_URL/recipe-parser/test-bullet-steps" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq '.'
echo ""

# 3. 测试单个菜谱导入
echo "3. 测试单个菜谱导入（白灼菜心）..."
SINGLE_RESULT=$(curl -s -X POST "$BASE_URL/recipe-parser/parse-and-save-local" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "categoryFolderName=vegetable_dish&fileName=白灼菜心.md")

echo "$SINGLE_RESULT" | jq '.'

RECIPE_ID=$(echo "$SINGLE_RESULT" | jq -r '.data')
if [ "$RECIPE_ID" != "null" ] && [ "$RECIPE_ID" != "" ]; then
    echo "✅ 单个菜谱导入成功，ID: $RECIPE_ID"
else
    echo "❌ 单个菜谱导入失败"
fi
echo ""

# 4. 测试批量导入（小批量测试）
echo "4. 测试批量导入本地菜谱..."
BATCH_RESULT=$(curl -s -X POST "$BASE_URL/recipe-parser/batch-import-local" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$BATCH_RESULT" | jq '.'

SUCCESS_COUNT=$(echo "$BATCH_RESULT" | jq -r '.data.totalSuccess // 0')
FAILED_COUNT=$(echo "$BATCH_RESULT" | jq -r '.data.totalFailed // 0')

echo "批量导入结果：成功 $SUCCESS_COUNT 个，失败 $FAILED_COUNT 个"
echo ""

# 5. 验证导入的菜谱
if [ "$RECIPE_ID" != "null" ] && [ "$RECIPE_ID" != "" ]; then
    echo "5. 验证导入的菜谱详情..."
    curl -s -X GET "$BASE_URL/recipe/detail/$RECIPE_ID" \
      -H "Authorization: Bearer $JWT_TOKEN" | jq '.'
    echo ""
fi

# 6. 搜索蔬菜类菜谱
echo "6. 搜索蔬菜类菜谱..."
curl -s -X POST "$BASE_URL/recipe/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "category": 2,
    "pageNum": 1,
    "pageSize": 5
  }' | jq '.'
echo ""

# 7. 统计各分类菜谱数量
echo "7. 统计各分类菜谱数量..."
for category in {0..9}; do
    COUNT=$(curl -s -X POST "$BASE_URL/recipe/search" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $JWT_TOKEN" \
      -d "{\"category\": $category, \"pageNum\": 1, \"pageSize\": 1}" | jq -r '.data.total // 0')
    
    case $category in
        0) CATEGORY_NAME="未知" ;;
        1) CATEGORY_NAME="荤菜" ;;
        2) CATEGORY_NAME="蔬菜" ;;
        3) CATEGORY_NAME="汤" ;;
        4) CATEGORY_NAME="海鲜" ;;
        5) CATEGORY_NAME="甜点" ;;
        6) CATEGORY_NAME="饮品" ;;
        7) CATEGORY_NAME="主食" ;;
        8) CATEGORY_NAME="调料" ;;
        9) CATEGORY_NAME="早餐" ;;
    esac
    
    echo "分类 $category ($CATEGORY_NAME): $COUNT 个菜谱"
done
echo ""

echo "=== 测试完成 ==="
