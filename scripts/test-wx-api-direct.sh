#!/bin/bash

# 直接测试微信API调用，验证text/plain问题是否解决
echo "=== 直接测试微信API调用 ==="

# 微信API参数
APPID="wxbe4f9d4bae7148d2"
SECRET="efb3ff0e65fa5224536aa0d1341fa2be"
TEST_CODE="test_invalid_code"
GRANT_TYPE="authorization_code"

# 构建URL
URL="https://api.weixin.qq.com/sns/jscode2session?appid=${APPID}&secret=${SECRET}&js_code=${TEST_CODE}&grant_type=${GRANT_TYPE}"

echo "测试URL: $URL"
echo ""

echo "1. 使用curl直接调用微信API..."
RESPONSE=$(curl -s "$URL")
echo "响应内容: $RESPONSE"
echo ""

echo "2. 检查响应的Content-Type..."
CONTENT_TYPE=$(curl -s -I "$URL" | grep -i "content-type")
echo "Content-Type: $CONTENT_TYPE"
echo ""

echo "3. 分析响应格式..."
if echo "$RESPONSE" | jq . > /dev/null 2>&1; then
    echo "✓ 响应是有效的JSON格式"
    echo "格式化的JSON:"
    echo "$RESPONSE" | jq .
else
    echo "✗ 响应不是有效的JSON格式"
fi

echo ""
echo "=== 测试结论 ==="
echo "如果看到有效的JSON响应但Content-Type是text/plain，"
echo "说明这就是导致Feign解析失败的原因。"
echo "我们的RestTemplate配置应该能够处理这种情况。"
