#!/bin/bash

# 微信登录接口测试脚本
# 用于验证修复后的微信登录功能

echo "=== 微信登录接口测试 ==="

# 服务器地址
SERVER_URL="http://localhost:8082"

# 测试数据
TEST_CODE="test_code_from_wx_miniapp"

echo "1. 测试微信登录接口..."
echo "请求URL: $SERVER_URL/user/wx-login"

# 发送登录请求
curl -X POST "$SERVER_URL/user/wx-login" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "'$TEST_CODE'",
    "userInfo": {
      "nickName": "测试用户",
      "avatarUrl": "https://example.com/avatar.jpg",
      "gender": 1,
      "country": "中国",
      "province": "广东省",
      "city": "深圳市",
      "language": "zh_CN"
    }
  }' \
  -w "\n\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s

echo ""
echo "=== 测试完成 ==="
echo ""
echo "注意事项："
echo "1. 需要使用真实的微信小程序code才能成功"
echo "2. 当前配置使用RestTemplate而非Feign"
echo "3. 如果要测试Feign，请修改配置 wx.miniapp.use-feign: true"
echo ""
echo "配置说明："
echo "- use-feign: true  -> 使用修复后的Feign客户端"
echo "- use-feign: false -> 使用RestTemplate备用方案"
