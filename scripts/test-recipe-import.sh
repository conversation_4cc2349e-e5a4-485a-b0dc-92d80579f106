#!/bin/bash

# 菜谱导入测试脚本
# 使用方法: ./test-recipe-import.sh [JWT_TOKEN]

BASE_URL="http://localhost:8080"
JWT_TOKEN=${1:-"your_jwt_token_here"}

echo "=== 菜谱导入测试脚本 ==="
echo "Base URL: $BASE_URL"
echo "JWT Token: ${JWT_TOKEN:0:20}..."
echo ""

# 1. 测试描述提取功能
echo "1. 测试描述提取功能..."
DESCRIPTION_RESULT=$(curl -s -X GET "$BASE_URL/recipe-parser/test-description" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$DESCRIPTION_RESULT" | jq '.'

# 验证描述是否正确提取
DESCRIPTION=$(echo "$DESCRIPTION_RESULT" | jq -r '.data.description')
echo "提取的描述: $DESCRIPTION"

if [[ "$DESCRIPTION" == *"白灼菜心是经典粤菜"* ]] && [[ "$DESCRIPTION" != *"标题必须是"* ]] && [[ "$DESCRIPTION" != *"没有拍照"* ]]; then
    echo "✅ 描述提取功能正常"
else
    echo "❌ 描述提取功能异常"
fi
echo ""

# 2. 测试解析白灼菜心示例
echo "2. 测试解析白灼菜心示例..."
curl -s -X GET "$BASE_URL/recipe-parser/parse-example" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq '.'
echo ""

# 3. 保存白灼菜心到数据库
echo "3. 保存白灼菜心到数据库..."
RECIPE_ID=$(curl -s -X POST "$BASE_URL/recipe-parser/save-baizhuocaixin" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq -r '.data')

if [ "$RECIPE_ID" != "null" ] && [ "$RECIPE_ID" != "" ]; then
    echo "✅ 菜谱保存成功，ID: $RECIPE_ID"
else
    echo "❌ 菜谱保存失败"
    exit 1
fi
echo ""

# 4. 查询保存的菜谱详情
echo "4. 查询菜谱详情 (ID: $RECIPE_ID)..."
curl -s -X GET "$BASE_URL/recipe/detail/$RECIPE_ID" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq '.'
echo ""

# 5. 搜索菜谱
echo "5. 搜索菜谱..."
curl -s -X POST "$BASE_URL/recipe/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "recipeName": "白灼菜心",
    "pageNum": 1,
    "pageSize": 10
  }' | jq '.'
echo ""

# 6. 根据食材搜索菜谱
echo "6. 根据食材搜索菜谱..."
curl -s -X GET "$BASE_URL/recipe/search-by-ingredients?ingredientNames=菜心,生抽&pageNum=1&pageSize=10" \
  -H "Authorization: Bearer $JWT_TOKEN" | jq '.'
echo ""

echo "=== 测试完成 ==="
