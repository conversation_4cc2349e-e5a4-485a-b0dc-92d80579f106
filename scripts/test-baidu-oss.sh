#!/bin/bash

# 百度OSS文件存储API测试脚本
# 使用方法: ./test-baidu-oss.sh [JWT_TOKEN] [TEST_FILE_PATH]

BASE_URL="http://localhost:8080"
JWT_TOKEN=${1:-"your_jwt_token_here"}
TEST_FILE=${2:-"test.jpg"}

echo "=== 百度OSS文件存储API测试脚本 ==="
echo "Base URL: $BASE_URL"
echo "JWT Token: ${JWT_TOKEN:0:20}..."
echo "Test File: $TEST_FILE"
echo ""

# 检查测试文件是否存在
if [ ! -f "$TEST_FILE" ]; then
    echo "❌ 测试文件不存在：$TEST_FILE"
    echo "请提供一个有效的测试文件路径，或创建一个测试文件"
    echo "例如：echo 'test content' > test.txt"
    exit 1
fi

# 0. 诊断百度OSS配置
echo "0. 诊断百度OSS配置..."
DIAGNOSE_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/diagnose" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$DIAGNOSE_RESULT" | jq -r '.data // "诊断失败"'
echo ""

# 0.1 测试百度OSS服务状态
echo "0.1 测试百度OSS服务状态..."
SERVICE_TEST_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/test" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$SERVICE_TEST_RESULT" | jq '.'

# 检查服务是否正常
SERVICE_CODE=$(echo "$SERVICE_TEST_RESULT" | jq -r '.code // 500')
if [ "$SERVICE_CODE" = "200" ]; then
    echo "✅ 百度OSS服务状态正常"
else
    echo "❌ 百度OSS服务异常，请检查配置和网络连接"
    echo "错误信息：$(echo "$SERVICE_TEST_RESULT" | jq -r '.msg // "未知错误"')"
fi
echo ""

# 1. 测试文件上传
echo "1. 测试文件上传..."
UPLOAD_RESULT=$(curl -s -X POST "$BASE_URL/baidu-oss/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@$TEST_FILE")

echo "$UPLOAD_RESULT" | jq '.'

# 提取文件ID
FILE_ID=$(echo "$UPLOAD_RESULT" | jq -r '.data.fileId // empty')
if [ -z "$FILE_ID" ] || [ "$FILE_ID" = "null" ]; then
    echo "❌ 文件上传失败，无法获取文件ID"
    exit 1
else
    echo "✅ 文件上传成功，文件ID: $FILE_ID"
fi
echo ""

# 2. 测试获取单个文件URL
echo "2. 测试获取单个文件URL..."
SINGLE_URL_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/url/$FILE_ID" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$SINGLE_URL_RESULT" | jq '.'

# 验证URL获取结果
FILE_URL=$(echo "$SINGLE_URL_RESULT" | jq -r '.data.fileUrl // empty')
if [ -z "$FILE_URL" ] || [ "$FILE_URL" = "null" ]; then
    echo "❌ 获取文件URL失败"
else
    echo "✅ 文件URL获取成功: $FILE_URL"
fi
echo ""

# 3. 测试批量获取文件URL
echo "3. 测试批量获取文件URL..."
# 使用相同的文件ID测试批量获取（实际使用中会是不同的文件ID）
BATCH_URL_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/urls?fileIds=$FILE_ID,$FILE_ID,$FILE_ID" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "$BATCH_URL_RESULT" | jq '.'

# 验证批量获取结果
SUCCESS_COUNT=$(echo "$BATCH_URL_RESULT" | jq -r '.data.successCount // 0')
FAIL_COUNT=$(echo "$BATCH_URL_RESULT" | jq -r '.data.failCount // 0')
echo "批量获取结果：成功 $SUCCESS_COUNT 个，失败 $FAIL_COUNT 个"
echo ""

# 4. 测试错误场景
echo "4. 测试错误场景..."

# 4.1 测试无效文件ID
echo "4.1 测试无效文件ID..."
INVALID_ID_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/url/invalid_id" \
  -H "Authorization: Bearer $JWT_TOKEN")
echo "$INVALID_ID_RESULT" | jq '.'
echo ""

# 4.2 测试空文件上传
echo "4.2 测试空文件上传..."
EMPTY_FILE_RESULT=$(curl -s -X POST "$BASE_URL/baidu-oss/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=")
echo "$EMPTY_FILE_RESULT" | jq '.'
echo ""

# 4.3 测试批量获取过多文件ID
echo "4.3 测试批量获取过多文件ID..."
# 生成超过50个文件ID的字符串
MANY_IDS=""
for i in {1..55}; do
    if [ $i -eq 1 ]; then
        MANY_IDS="$i"
    else
        MANY_IDS="$MANY_IDS,$i"
    fi
done

TOO_MANY_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/urls?fileIds=$MANY_IDS" \
  -H "Authorization: Bearer $JWT_TOKEN")
echo "$TOO_MANY_RESULT" | jq '.'
echo ""

# 5. 性能测试（可选）
echo "5. 性能测试..."
echo "5.1 测试并发上传（3个文件）..."

# 创建临时测试文件
echo "test content 1" > temp_test1.txt
echo "test content 2" > temp_test2.txt
echo "test content 3" > temp_test3.txt

# 并发上传
(curl -s -X POST "$BASE_URL/baidu-oss/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@temp_test1.txt" > upload1.json) &

(curl -s -X POST "$BASE_URL/baidu-oss/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@temp_test2.txt" > upload2.json) &

(curl -s -X POST "$BASE_URL/baidu-oss/upload" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -F "file=@temp_test3.txt" > upload3.json) &

# 等待所有上传完成
wait

echo "并发上传结果："
echo "文件1："
cat upload1.json | jq '.'
echo "文件2："
cat upload2.json | jq '.'
echo "文件3："
cat upload3.json | jq '.'

# 清理临时文件
rm -f temp_test1.txt temp_test2.txt temp_test3.txt
rm -f upload1.json upload2.json upload3.json

echo ""

# 6. 总结
echo "6. 测试总结..."
# 7. 检查上传的文件
if [ -n "$FILE_ID" ] && [ "$FILE_ID" != "null" ]; then
    echo "7. 检查上传的文件..."
    FILE_CHECK_RESULT=$(curl -s -X GET "$BASE_URL/baidu-oss/check/$FILE_ID" \
      -H "Authorization: Bearer $JWT_TOKEN")

    echo "$FILE_CHECK_RESULT" | jq '.'

    # 测试生成的URL是否可访问
    FILE_URL_FROM_CHECK=$(echo "$FILE_CHECK_RESULT" | jq -r '.data.fileUrl // empty')
    if [ -n "$FILE_URL_FROM_CHECK" ] && [ "$FILE_URL_FROM_CHECK" != "null" ]; then
        echo "测试URL访问性..."
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$FILE_URL_FROM_CHECK")
        echo "URL访问状态码：$HTTP_STATUS"

        if [ "$HTTP_STATUS" = "200" ]; then
            echo "✅ URL访问成功"
        else
            echo "❌ URL访问失败，状态码：$HTTP_STATUS"
            echo "请检查权限配置和Bucket设置"
        fi
    fi
    echo ""
fi

# 8. 总结
echo "8. 测试总结..."
if [ -n "$FILE_ID" ] && [ "$FILE_ID" != "null" ] && [ -n "$FILE_URL" ] && [ "$FILE_URL" != "null" ]; then
    echo "✅ 所有核心功能测试通过"
    echo "   - 配置诊断：完成"
    echo "   - 文件上传：成功"
    echo "   - 单个URL获取：成功"
    echo "   - 批量URL获取：成功"
    echo "   - 文件检查：完成"
    echo "   - 错误处理：正常"
else
    echo "❌ 部分功能测试失败"
    echo "请查看上面的诊断信息和错误日志"
fi

echo ""
echo "=== 测试完成 ==="

# 显示使用建议
echo ""
echo "使用建议："
echo "1. 确保百度OSS配置正确"
echo "2. 检查JWT token是否有效"
echo "3. 验证网络连接是否正常"
echo "4. 查看应用日志获取详细错误信息"
